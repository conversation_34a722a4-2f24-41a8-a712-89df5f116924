<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1" />
    <title>SkyVPS360 Client Portal</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css"
      integrity="sha512-9usAa10IRO0HhonpyAIVpjrylPvoDwiPUiKdWk5t3PyolY1cOd4DSE0Ga+ri4AuTroPR5aQvXU9xC6qOPnzFeg=="
      crossorigin="anonymous" referrerpolicy="no-referrer" />

    <!-- Theme initialization script - runs before React loads -->
    <script>
      (function() {
        try {
          // Check if we're in admin area
          const isAdminArea = window.location.pathname.startsWith('/admin');

          if (isAdminArea) {
            // Use admin theme (default to dark)
            const adminTheme = localStorage.getItem('admin-theme') || 'dark';
            document.documentElement.classList.remove('light', 'dark');
            document.documentElement.classList.add(adminTheme);
            console.log('Pre-React admin theme initialization:', adminTheme);
          } else {
            // For frontend, always use light theme (no dark mode for frontend)
            document.documentElement.classList.remove('light', 'dark');
            document.documentElement.classList.add('light');
            console.log('Pre-React frontend theme initialization: light');
          }
        } catch (e) {
          console.error('Theme initialization error:', e);
          document.documentElement.classList.add('light');
        }
      })();
    </script>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
    <!-- Fix for share modal error - safely handles null elements when event listeners are applied -->
    <script type="text/javascript" src="/js/share-modal-fix.js"></script>
    <!-- This is a replit script which adds a banner on the top of the page when opened in development mode outside the replit environment -->
    <!-- <script type="text/javascript" src="https://replit.com/public/js/replit-dev-banner.js"></script> -->
  </body>
</html>