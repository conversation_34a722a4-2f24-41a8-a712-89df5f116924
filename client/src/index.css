@import url('./styles/markdown.css');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Global override for Input focus styles using brand colors */
.input-brand-focus {
  @apply focus-visible:ring-2 focus-visible:ring-offset-2;
  --tw-ring-color: var(--brand-primary-light) !important;
  --tw-ring-opacity: 1 !important;
  outline: none !important;
}

:root {
  --background: 210 40% 98%;
  --foreground: 215 25% 27%;
  --muted: 214 32% 91%;
  --muted-foreground: 215 16% 47%;
  --popover: 0 0% 100%;
  --popover-foreground: 215 25% 27%;
  --card: 0 0% 100%;
  --card-foreground: 215 25% 27%;
  --border: 214.3 31.8% 91.4%;
  --input: 214.3 31.8% 91.4%;
  --primary: 215 90% 54%;
  --primary-foreground: 210 40% 98%;
  --secondary: 222 47% 11%;
  --secondary-foreground: 210 40% 98%;
  --accent: 215 90% 54%;
  --accent-foreground: 210 40% 98%;
  --destructive: 0 84% 60%;
  --destructive-foreground: 210 40% 98%;
  --ring: 215 90% 54%;
  --radius: 0.5rem;
  --warning: 38 92% 50%;
  --warning-foreground: 210 40% 98%;
  --chart-1: 215 90% 54%;
  --chart-2: 262 83% 58%;
  --chart-3: 0 84% 60%;
  --chart-4: 215 90% 54%;
  --chart-5: 30 95% 60%;
  --sidebar-background: 222 47% 11%;
  --sidebar-foreground: 210 40% 98%;
  --sidebar-primary: 215 90% 54%;
  --sidebar-primary-foreground: 210 40% 98%;
  --sidebar-accent: 217 33% 17%;
  --sidebar-accent-foreground: 210 40% 98%;
  --sidebar-border: 216 34% 17%;
  --sidebar-ring: 215 90% 54%;
}

.dark {
  --background: 222 47% 11%;
  --foreground: 210 40% 98%;
  --muted: 217 33% 17%;
  --muted-foreground: 215 20% 65%;
  --popover: 222 47% 11%;
  --popover-foreground: 210 40% 98%;
  --card: 222 47% 11%;
  --card-foreground: 210 40% 98%;
  --border: 217 33% 17%;
  --input: 217 33% 17%;
  --primary: 215 90% 54%;
  --primary-foreground: 210 40% 98%;
  --secondary: 217 33% 17%;
  --secondary-foreground: 210 40% 98%;
  --accent: 215 90% 54%;
  --accent-foreground: 210 40% 98%;
  --destructive: 0 62.8% 30.6%;
  --destructive-foreground: 210 40% 98%;
  --warning: 38 92% 50%;
  --warning-foreground: 210 40% 98%;
  --ring: 212.7 26.8% 83.9%;
  --chart-1: 215 90% 54%;
  --chart-2: 262 83% 58%;
  --chart-3: 0 84% 60%;
  --chart-4: 215 90% 54%;
  --chart-5: 30 95% 60%;
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply font-sans antialiased bg-background text-foreground;
  }
}

/* PayPal Button Styles */
.paypal-button-container {
  max-width: 500px;
  margin: 0 auto;
}

/* Brand Color Utilities - Updated to use HSL primary color variables */
.text-brand {
  color: hsl(var(--primary)) !important;
}

.bg-brand {
  background-color: hsl(var(--primary)) !important;
}

.bg-brand-light {
  background-color: hsl(var(--primary) / 0.1) !important;
}

.bg-brand-lighter {
  background-color: hsl(var(--primary) / 0.04) !important;
}

.border-brand {
  border-color: hsl(var(--primary)) !important;
}

.hover\:text-brand:hover {
  color: hsl(var(--primary)) !important;
}

.hover\:bg-brand:hover {
  background-color: hsl(var(--primary)) !important;
}

/* Dynamic navbar hover effect */
.nav-button-dynamic:hover {
  background-color: var(--hover-bg-color, hsl(var(--primary) / 0.04)) !important;
  color: var(--hover-text-color, hsl(var(--primary))) !important;
}

/* Override Shadcn Button hover, focus, and active styles for nav buttons */
button[data-nav-button="true"]:hover,
.button[data-nav-button="true"]:hover {
  background-color: var(--hover-bg-color, hsl(var(--primary) / 0.04)) !important;
  color: var(--hover-text-color, hsl(var(--primary))) !important;
}

button[data-nav-button="true"]:focus,
.button[data-nav-button="true"]:focus {
  background-color: var(--hover-bg-color, hsl(var(--primary) / 0.04)) !important;
  color: var(--hover-text-color, hsl(var(--primary))) !important;
  outline-color: var(--focus-ring-color, hsl(var(--primary))) !important;
}

button[data-nav-button="true"]:active,
.button[data-nav-button="true"]:active {
  background-color: var(--active-bg-color, hsl(var(--primary) / 0.1)) !important;
  color: var(--hover-text-color, hsl(var(--primary))) !important;
  outline-color: var(--focus-ring-color, hsl(var(--primary))) !important;
}

/* Override default focus ring color */
.ring-ring {
  --ring: hsl(var(--primary)) !important;
}

.hover\:bg-brand-light:hover {
  background-color: hsl(var(--primary) / 0.1) !important;
}

.hover\:border-brand:hover {
  border-color: hsl(var(--primary)) !important;
}

/* Profile Dropdown Menu Styling */
.profile-dropdown-content [data-radix-collection-item]:hover {
  background-color: hsl(var(--primary) / 0.1) !important;
}

.profile-dropdown-content [data-radix-collection-item]:active {
  background-color: hsl(var(--primary) / 0.2) !important;
}

/* Strengthen the selector to override Radix UI's default styles */
.profile-dropdown-content [role="menuitem"]:hover {
  background-color: hsl(var(--primary) / 0.1) !important;
  color: hsl(var(--primary)) !important;
}

.profile-dropdown-content [role="menuitem"]:active {
  background-color: hsl(var(--primary) / 0.2) !important;
}

/* Custom dropdown menu links styling */
.dropdown-menu-link {
  transition: color 0.2s ease;
}

.dropdown-menu-link:hover {
  color: hsl(var(--primary)) !important;
}

/* Direct targeting of dropdown menu content to override Radix defaults */
[data-radix-popper-content-wrapper] [role="menu"] [role="menuitem"]:hover {
  background-color: hsl(var(--primary) / 0.1) !important;
  color: hsl(var(--primary)) !important;
}

/* Custom Scrollbar Styles */
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: hsl(var(--muted));
  border-radius: 6px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: hsl(var(--muted-foreground));
  border-radius: 6px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--primary));
}

/* Documentation page styles using CSS variables */
.docs-page a,
.docs-page button,
.docs-page [role="link"] {
  color: var(--brand-primary) !important;
}

.docs-page a:hover,
.docs-page button:hover {
  color: var(--brand-primary) !important;
}
