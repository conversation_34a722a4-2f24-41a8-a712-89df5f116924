import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Slider } from "@/components/ui/slider";
import { Input } from "@/components/ui/input";
import { Switch } from "@/components/ui/switch";
import { Separator } from "@/components/ui/separator";
import { 
  CloudPricingConfig, 
  ServerConfiguration, 
  calculateCloudPricing, 
  formatPrice, 
  formatResource,
  validateServerConfig 
} from "@/lib/pricing";
import { Cpu, MemoryStick, HardDrive, Network, Globe } from "lucide-react";

interface CloudConfigurationProps {
  configuration: ServerConfiguration;
  onConfigurationChange: (config: ServerConfiguration) => void;
  pricingConfig: CloudPricingConfig;
}

export function CloudConfiguration({
  configuration,
  onConfigurationChange,
  pricingConfig,
}: CloudConfigurationProps) {
  const [localConfig, setLocalConfig] = useState<ServerConfiguration>(configuration);

  // Update local config when prop changes
  useEffect(() => {
    setLocalConfig(configuration);
  }, [configuration]);

  // Update parent when local config changes
  const updateConfiguration = (updates: Partial<ServerConfiguration>) => {
    const newConfig = { ...localConfig, ...updates };
    setLocalConfig(newConfig);
    onConfigurationChange(newConfig);
  };

  // Calculate pricing breakdown
  const pricingBreakdown = calculateCloudPricing(localConfig, pricingConfig);
  const validationErrors = validateServerConfig(localConfig);

  return (
    <div className="space-y-6">
      {/* CPU Configuration */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2 text-lg">
            <Cpu className="h-5 w-5" />
            CPU Cores
          </CardTitle>
          <CardDescription>
            Number of virtual CPU cores for your server
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <Label>Cores: {localConfig.cpuCores}</Label>
              {pricingConfig.cpuPricingEnabled && (
                <span className="text-sm text-muted-foreground">
                  {formatPrice(pricingBreakdown.cpu)}/mo
                </span>
              )}
            </div>
            <Slider
              value={[localConfig.cpuCores]}
              onValueChange={([value]) => updateConfiguration({ cpuCores: value })}
              min={1}
              max={32}
              step={1}
              className="w-full"
            />
            <div className="flex justify-between text-xs text-muted-foreground">
              <span>1 Core</span>
              <span>32 Cores</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Memory Configuration */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2 text-lg">
            <MemoryStick className="h-5 w-5" />
            Memory (RAM)
          </CardTitle>
          <CardDescription>
            Amount of system memory for your server
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <Label>Memory: {formatResource(localConfig.memory / 1024, 'GB')}</Label>
              {pricingConfig.ramPricingEnabled && (
                <span className="text-sm text-muted-foreground">
                  {formatPrice(pricingBreakdown.memory)}/mo
                </span>
              )}
            </div>
            <Slider
              value={[localConfig.memory]}
              onValueChange={([value]) => updateConfiguration({ memory: value })}
              min={512}
              max={131072} // 128 GB
              step={512}
              className="w-full"
            />
            <div className="flex justify-between text-xs text-muted-foreground">
              <span>512 MB</span>
              <span>128 GB</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Storage Configuration */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2 text-lg">
            <HardDrive className="h-5 w-5" />
            Storage
          </CardTitle>
          <CardDescription>
            Primary storage capacity for your server
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <Label>Storage: {formatResource(localConfig.storage, 'GB')}</Label>
              {pricingConfig.storagePricingEnabled && (
                <span className="text-sm text-muted-foreground">
                  {formatPrice(pricingBreakdown.storage)}/mo
                </span>
              )}
            </div>
            <Slider
              value={[localConfig.storage]}
              onValueChange={([value]) => updateConfiguration({ storage: value })}
              min={10}
              max={2048} // 2 TB
              step={5}
              className="w-full"
            />
            <div className="flex justify-between text-xs text-muted-foreground">
              <span>10 GB</span>
              <span>2 TB</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Network Configuration */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2 text-lg">
            <Network className="h-5 w-5" />
            Network Speed
          </CardTitle>
          <CardDescription>
            Network bandwidth allocation for your server
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <Label>Speed: {localConfig.networkSpeed} Mbps</Label>
              {pricingConfig.networkPricingEnabled && (
                <span className="text-sm text-muted-foreground">
                  {formatPrice(pricingBreakdown.network)}/mo
                </span>
              )}
            </div>
            <Slider
              value={[localConfig.networkSpeed]}
              onValueChange={([value]) => updateConfiguration({ networkSpeed: value })}
              min={10}
              max={10000} // 10 Gbps
              step={10}
              className="w-full"
            />
            <div className="flex justify-between text-xs text-muted-foreground">
              <span>10 Mbps</span>
              <span>10 Gbps</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* IP Address Configuration */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2 text-lg">
            <Globe className="h-5 w-5" />
            IP Addresses
          </CardTitle>
          <CardDescription>
            Configure IP address options for your server
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Public IPv4 */}
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label>Public IPv4 Address</Label>
              <p className="text-sm text-muted-foreground">
                Dedicated public IPv4 address
              </p>
            </div>
            <div className="flex items-center gap-2">
              {pricingConfig.publicIpv4PricingEnabled && localConfig.publicIpv4 && (
                <span className="text-sm text-muted-foreground">
                  {formatPrice(pricingBreakdown.publicIpv4)}/mo
                </span>
              )}
              <Switch
                checked={localConfig.publicIpv4}
                onCheckedChange={(checked) => updateConfiguration({ publicIpv4: checked })}
              />
            </div>
          </div>

          <Separator />

          {/* NAT IPv4 */}
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label>NAT IPv4 Address</Label>
              <p className="text-sm text-muted-foreground">
                Shared IPv4 address with port forwarding
              </p>
            </div>
            <div className="flex items-center gap-2">
              {pricingConfig.natIpv4PricingEnabled && localConfig.natIpv4 && (
                <span className="text-sm text-muted-foreground">
                  {formatPrice(pricingBreakdown.natIpv4)}/mo
                </span>
              )}
              <Switch
                checked={localConfig.natIpv4}
                onCheckedChange={(checked) => updateConfiguration({ natIpv4: checked })}
              />
            </div>
          </div>

          <Separator />

          {/* Additional IPv6 */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <Label>Additional IPv6 Addresses</Label>
              {pricingConfig.publicIpv6PricingEnabled && localConfig.additionalIpv6 > 0 && (
                <span className="text-sm text-muted-foreground">
                  {formatPrice(pricingBreakdown.publicIpv6)}/mo
                </span>
              )}
            </div>
            <Input
              type="number"
              min="0"
              max="100"
              value={localConfig.additionalIpv6}
              onChange={(e) => updateConfiguration({ additionalIpv6: parseInt(e.target.value) || 0 })}
              className="w-full"
            />
          </div>
        </CardContent>
      </Card>

      {/* Pricing Summary */}
      <Card>
        <CardHeader>
          <CardTitle>Pricing Summary</CardTitle>
          <CardDescription>
            Estimated monthly cost breakdown
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            {pricingConfig.cpuPricingEnabled && (
              <div className="flex justify-between text-sm">
                <span>CPU ({localConfig.cpuCores} cores)</span>
                <span>{formatPrice(pricingBreakdown.cpu)}</span>
              </div>
            )}
            {pricingConfig.ramPricingEnabled && (
              <div className="flex justify-between text-sm">
                <span>Memory ({formatResource(localConfig.memory / 1024, 'GB')})</span>
                <span>{formatPrice(pricingBreakdown.memory)}</span>
              </div>
            )}
            {pricingConfig.storagePricingEnabled && (
              <div className="flex justify-between text-sm">
                <span>Storage ({formatResource(localConfig.storage, 'GB')})</span>
                <span>{formatPrice(pricingBreakdown.storage)}</span>
              </div>
            )}
            {pricingConfig.networkPricingEnabled && (
              <div className="flex justify-between text-sm">
                <span>Network ({localConfig.networkSpeed} Mbps)</span>
                <span>{formatPrice(pricingBreakdown.network)}</span>
              </div>
            )}
            {pricingConfig.publicIpv4PricingEnabled && localConfig.publicIpv4 && (
              <div className="flex justify-between text-sm">
                <span>Public IPv4</span>
                <span>{formatPrice(pricingBreakdown.publicIpv4)}</span>
              </div>
            )}
            {pricingConfig.natIpv4PricingEnabled && localConfig.natIpv4 && (
              <div className="flex justify-between text-sm">
                <span>NAT IPv4</span>
                <span>{formatPrice(pricingBreakdown.natIpv4)}</span>
              </div>
            )}
            {pricingConfig.publicIpv6PricingEnabled && localConfig.additionalIpv6 > 0 && (
              <div className="flex justify-between text-sm">
                <span>Additional IPv6 ({localConfig.additionalIpv6})</span>
                <span>{formatPrice(pricingBreakdown.publicIpv6)}</span>
              </div>
            )}
            <Separator />
            <div className="flex justify-between font-medium">
              <span>Total Monthly Cost</span>
              <span>{formatPrice(pricingBreakdown.total)}</span>
            </div>
          </div>

          {/* Validation Errors */}
          {validationErrors.length > 0 && (
            <div className="mt-4 p-3 bg-destructive/10 border border-destructive/20 rounded-md">
              <p className="text-sm font-medium text-destructive mb-1">Configuration Issues:</p>
              <ul className="text-sm text-destructive space-y-1">
                {validationErrors.map((error, index) => (
                  <li key={index}>• {error}</li>
                ))}
              </ul>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
