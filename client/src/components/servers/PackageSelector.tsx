import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { formatPrice, formatResource } from "@/lib/pricing";
import { Check, Cpu, HardDrive, MemoryStick, Network } from "lucide-react";

export interface Package {
  id: number;
  name: string;
  description?: string;
  cpu: number;
  memory: number; // in MB
  storage: number; // in GB
  traffic: number; // in GB
  enabled: boolean;
  pricing?: {
    price: number; // in dollars
    displayOrder: number;
    enabled: boolean;
  } | null;
}

interface PackageSelectorProps {
  packages: Package[];
  selectedPackageId: number | null;
  onPackageSelect: (packageId: number) => void;
  isLoading?: boolean;
}

export function PackageSelector({
  packages,
  selectedPackageId,
  onPackageSelect,
  isLoading = false,
}: PackageSelectorProps) {
  if (isLoading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {Array.from({ length: 6 }).map((_, i) => (
          <Card key={i}>
            <CardHeader>
              <Skeleton className="h-6 w-3/4" />
              <Skeleton className="h-4 w-1/2" />
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-2/3" />
                <Skeleton className="h-8 w-full" />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (!packages.length) {
    return (
      <Card>
        <CardContent className="text-center py-12">
          <p className="text-muted-foreground">
            No packages are currently available. Please contact support.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      {packages.map((pkg) => {
        const isSelected = selectedPackageId === pkg.id;
        const hasValidPricing = pkg.pricing && pkg.pricing.enabled;
        
        return (
          <Card
            key={pkg.id}
            className={`cursor-pointer transition-all duration-200 hover:shadow-md ${
              isSelected
                ? "ring-2 ring-primary border-primary"
                : "hover:border-primary/50"
            } ${!hasValidPricing ? "opacity-60" : ""}`}
            onClick={() => hasValidPricing && onPackageSelect(pkg.id)}
          >
            <CardHeader className="pb-3">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <CardTitle className="text-lg flex items-center gap-2">
                    {pkg.name}
                    {isSelected && (
                      <Check className="h-4 w-4 text-primary" />
                    )}
                  </CardTitle>
                  {pkg.description && (
                    <CardDescription className="mt-1">
                      {pkg.description}
                    </CardDescription>
                  )}
                </div>
                {hasValidPricing ? (
                  <Badge variant="secondary" className="ml-2">
                    {formatPrice(pkg.pricing!.price)}/mo
                  </Badge>
                ) : (
                  <Badge variant="destructive" className="ml-2">
                    Unavailable
                  </Badge>
                )}
              </div>
            </CardHeader>
            
            <CardContent className="space-y-3">
              {/* Resource specifications */}
              <div className="grid grid-cols-2 gap-3 text-sm">
                <div className="flex items-center gap-2">
                  <Cpu className="h-4 w-4 text-muted-foreground" />
                  <span>{pkg.cpu} {pkg.cpu === 1 ? 'Core' : 'Cores'}</span>
                </div>
                
                <div className="flex items-center gap-2">
                  <MemoryStick className="h-4 w-4 text-muted-foreground" />
                  <span>{formatResource(pkg.memory / 1024, 'GB')} RAM</span>
                </div>
                
                <div className="flex items-center gap-2">
                  <HardDrive className="h-4 w-4 text-muted-foreground" />
                  <span>{formatResource(pkg.storage, 'GB')} Storage</span>
                </div>
                
                <div className="flex items-center gap-2">
                  <Network className="h-4 w-4 text-muted-foreground" />
                  <span>{formatResource(pkg.traffic, 'GB')} Transfer</span>
                </div>
              </div>

              {/* Selection button */}
              <Button
                variant={isSelected ? "default" : "outline"}
                className="w-full"
                disabled={!hasValidPricing}
                onClick={(e) => {
                  e.stopPropagation();
                  if (hasValidPricing) {
                    onPackageSelect(pkg.id);
                  }
                }}
              >
                {isSelected ? "Selected" : hasValidPricing ? "Select Package" : "Not Available"}
              </Button>
            </CardContent>
          </Card>
        );
      })}
    </div>
  );
}
