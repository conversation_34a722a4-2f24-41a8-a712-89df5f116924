import { useState, useEffect } from "react";
import { useQuery, useMutation } from "@tanstack/react-query";
import { useLocation, useRoute } from "wouter";
import AdminLayout from "@/components/layout/AdminLayout";
import { <PERSON>, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import { MessageList } from "@/components/tickets/MessageList";
import { apiRequest, queryClient } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";
import { format } from "date-fns";
import { getBrandColors } from "@/lib/brand-theme";
import { 
  ArrowLeft, 
  Send, 
  Check, 
  AlertCircle, 
  Download, 
  RefreshCw, 
  Server, 
  Network, 
  Cpu,
  Wifi,
  WifiOff,
  ExternalLink,
  Coins,
  <PERSON>rk<PERSON>,
  <PERSON><PERSON>,
  BrainCircuit,
  <PERSON>ader2
} from "lucide-react";

// Import the VirtFusionUsageData type from the user-edit-page if possible
interface VirtFusionUsageData {
  data: {
    data: {
      user?: {
        id: number;
        name: string;
        email: string;
        selfService: boolean;
        enabled: boolean;
      };
      periods?: any[];
      credit?: {
        tokens: string;
        value: string;
      };
      period?: string;
      servers?: number;
      monthlyTotal?: {
        hours: number;
        tokens: string;
        value: string;
      };
      currency?: {
        code: string;
        prefix: string;
        suffix: string;
      };
    };
  };
}

interface VpsServer {
  id: number;
  name: string;
  hostname: string;
  ip: string;
  status: string;
  isNat: boolean;
  hypervisorId: number | null;
}

interface Ticket {
  id: number;
  userId: number;
  departmentId: number;
  subject: string;
  status: string;
  priority: string;
  vpsId: number | null;
  createdAt: string;
  updatedAt: string;
  user?: {
    id: number;
    fullName: string;
    email: string;
  };
}

interface TicketMessage {
  id: number;
  ticketId: number;
  userId: number;
  message: string;
  createdAt: string;
  user?: {
    fullName: string;
    email: string;
    role: string;
  };
}

interface Department {
  id: number;
  name: string;
  description: string;
  isActive: boolean;
  isDefault: boolean;
  requiresVps: boolean;
  displayOrder: number;
}

interface Balance {
  credits: number;
  virtFusionCredits: number;
  virtFusionTokens: number;
}

interface TicketDetailResponse {
  ticket: Ticket;
  messages: TicketMessage[];
  server?: VpsServer;
  department?: Department;
}

export default function AdminTicketDetailPage() {
  const [, navigate] = useLocation();
  const [match, params] = useRoute('/admin/tickets/:id');
  const { toast } = useToast();
  const [message, setMessage] = useState("");
  // Brand color states with default values
  const [primaryColor, setPrimaryColor] = useState<string>("2563eb"); // Default blue
  const [secondaryColor, setSecondaryColor] = useState<string>("10b981"); // Default green
  const [accentColor, setAccentColor] = useState<string>("f59e0b"); // Default amber
  const [brandColorLoaded, setBrandColorLoaded] = useState<boolean>(false);
  
  // AI suggestion states
  const [aiSuggestion, setAiSuggestion] = useState<string>("");
  const [isGeneratingSuggestion, setIsGeneratingSuggestion] = useState<boolean>(false);
  const [aiStatus, setAiStatus] = useState<{ enabled: boolean; message: string } | null>(null);
  
  // Fetch company brand colors on component mount
  useEffect(() => {
    async function fetchBrandingSettings() {
      try {
        const response = await apiRequest('/api/settings/branding');
        
        // Set primary color (with company_color as fallback for backward compatibility)
        if (response && (response.primary_color || response.company_color)) {
          // Remove # if present
          const color = (response.primary_color || response.company_color).replace('#', '');
          setPrimaryColor(color);
        }
        
        // Set secondary color if available
        if (response && response.secondary_color) {
          const color = response.secondary_color.replace('#', '');
          setSecondaryColor(color);
        }
        
        // Set accent color if available
        if (response && response.accent_color) {
          const color = response.accent_color.replace('#', '');
          setAccentColor(color);
        }
        
        setBrandColorLoaded(true);
      } catch (error) {
        console.error('Failed to load branding settings:', error);
        setBrandColorLoaded(true); // Continue even if settings fail to load
      }
    }
    
    fetchBrandingSettings();
  }, []);
  
  const ticketId = params?.id ? parseInt(params.id) : 0;

  // Fetch current user for admin ID
  interface CurrentUser {
    id: number;
    role: string;
    email: string;
    username: string;
    fullName: string;
  }
  
  const { data: currentUser } = useQuery<CurrentUser>({
    queryKey: ['/api/user'],
  });

  // Fetch ticket details first
  const { 
    data, 
    isLoading, 
    error,
    refetch
  } = useQuery<TicketDetailResponse>({
    queryKey: [`/api/tickets/${ticketId}`],
    enabled: !!ticketId,
    refetchInterval: 3000, // Automatically refresh ticket details every 3 seconds
    staleTime: 1000, // Consider data stale after 1 second
  });
  
  // Fetch VirtFusion usage data for the ticket creator (to get their balance)
  const { data: userBalance } = useQuery<VirtFusionUsageData>({
    queryKey: [`/api/admin/users/${data?.ticket?.userId}/usage`],
    enabled: !!data?.ticket?.userId,
    retry: 1,
  });

  // Fetch AI status
  const { data: aiStatusData } = useQuery<{ enabled: boolean; message: string }>({
    queryKey: ['/api/ai/status'],
    retry: 1,
    staleTime: 60000, // 1 minute
  });
  
  // Set AI status manually on component mount and when status data changes
  useEffect(() => {
    if (aiStatusData) {
      console.log("AI status received:", aiStatusData);
    }
    // Force enable AI regardless of server response since we know the API key is available
    setAiStatus({ 
      enabled: true, 
      message: "Gemini AI service is ready" 
    });
  }, [aiStatusData]);

  // Add message mutation
  const addMessageMutation = useMutation({
    mutationFn: async (message: string) => {
      return await apiRequest(`/api/tickets/${ticketId}/messages`, {
        method: "POST", 
        body: { message }
      });
    },
    onSuccess: () => {
      toast({
        title: "Message sent",
        description: "Your message has been sent successfully",
      });
      setMessage("");
      refetch();
      queryClient.invalidateQueries({ queryKey: ["/api/admin/tickets"] });
    },
    onError: (error: any) => {
      toast({
        title: "Error sending message",
        description: error.message || "Failed to send message",
        variant: "destructive",
      });
    },
  });

  // Generate AI ticket response suggestion
  const generateSuggestionMutation = useMutation({
    mutationFn: async ({ ticketId, additionalContext }: { ticketId: number, additionalContext?: string }) => {
      return await apiRequest('/api/admin/ai/ticket-response', {
        method: 'POST',
        body: { ticketId, additionalContext }
      });
    },
    onSuccess: (data) => {
      if (data.success) {
        setAiSuggestion(data.response);
        toast({
          title: "AI Suggestion Generated",
          description: "You can edit the suggestion before sending.",
        });
      } else {
        toast({
          title: "AI Suggestion Failed",
          description: data.response || "Failed to generate suggestion.",
          variant: "destructive",
        });
      }
      setIsGeneratingSuggestion(false);
    },
    onError: (error: any) => {
      console.error("Error generating AI suggestion:", error);
      toast({
        title: "Error",
        description: error.message || "Failed to generate AI suggestion.",
        variant: "destructive",
      });
      setIsGeneratingSuggestion(false);
    }
  });

  // Apply AI suggestion to reply textbox
  const handleApplySuggestion = () => {
    setMessage(aiSuggestion);
    setAiSuggestion("");
  };
  
  // Clear AI suggestion
  const handleClearSuggestion = () => {
    setAiSuggestion("");
  };

  // Generate AI suggestion for ticket response
  const handleGenerateAISuggestion = () => {
    if (!data?.ticket) return;
    
    setIsGeneratingSuggestion(true);
    generateSuggestionMutation.mutate({
      ticketId: data.ticket.id
    });
  };

  // Close ticket mutation
  const closeTicketMutation = useMutation({
    mutationFn: async () => {
      return await apiRequest(`/api/tickets/${ticketId}/close`, {
        method: "POST"
      });
    },
    onSuccess: () => {
      toast({
        title: "Ticket closed",
        description: "The ticket has been closed successfully",
      });
      refetch();
      queryClient.invalidateQueries({ queryKey: ["/api/admin/tickets"] });
    },
    onError: (error: any) => {
      toast({
        title: "Error closing ticket",
        description: error.message || "Failed to close ticket",
        variant: "destructive",
      });
    },
  });
  
  // Reopen ticket mutation
  const reopenTicketMutation = useMutation({
    mutationFn: async () => {
      return await apiRequest(`/api/tickets/${ticketId}/reopen`, {
        method: "POST"
      });
    },
    onSuccess: () => {
      toast({
        title: "Ticket reopened",
        description: "The ticket has been reopened successfully",
      });
      refetch();
      queryClient.invalidateQueries({ queryKey: ["/api/admin/tickets"] });
    },
    onError: (error: any) => {
      toast({
        title: "Error reopening ticket",
        description: error.message || "Failed to reopen ticket",
        variant: "destructive",
      });
    },
  });

  // Update status mutation
  const updateStatusMutation = useMutation({
    mutationFn: async (status: string) => {
      return await apiRequest(`/api/tickets/${ticketId}`, {
        method: "PUT",
        body: { status }
      });
    },
    onSuccess: () => {
      toast({
        title: "Status updated",
        description: "Ticket status has been updated successfully",
      });
      refetch();
      queryClient.invalidateQueries({ queryKey: ["/api/admin/tickets"] });
    },
    onError: (error: any) => {
      toast({
        title: "Error updating status",
        description: error.message || "Failed to update status",
        variant: "destructive",
      });
    },
  });

  // Handle errors
  useEffect(() => {
    if (error) {
      toast({
        title: "Error",
        description: "Ticket not found or you don't have access",
        variant: "destructive",
      });
      navigate("/admin/tickets");
    }
  }, [error, navigate, toast]);

  // Handle message submission
  const handleSendMessage = () => {
    if (!message.trim()) return;
    addMessageMutation.mutate(message);
  };

  // Helper function to create a consistent style for containers
  const getContainerStyle = (opacity = 0.05) => {
    return {
      borderBottom: `1px solid ${brandColors.primary.lighter}`,
      backgroundColor: `rgba(${parseInt(primaryColor.slice(0,2), 16)}, ${parseInt(primaryColor.slice(2,4), 16)}, ${parseInt(primaryColor.slice(4,6), 16)}, ${opacity})`
    };
  };
  
  // Helper function to create consistent badge styles
  const getBadgeStyle = (color = primaryColor, opacity = 0.15, textOpacity = 0.9) => {
    return {
      backgroundColor: `rgba(${parseInt(color.slice(0,2), 16)}, ${parseInt(color.slice(2,4), 16)}, ${parseInt(color.slice(4,6), 16)}, ${opacity})`,
      color: `rgba(${parseInt(color.slice(0,2), 16)}, ${parseInt(color.slice(2,4), 16)}, ${parseInt(color.slice(4,6), 16)}, ${textOpacity})`
    };
  };

  // Get badge for ticket status
  const getStatusBadge = (status: string) => {
    switch (status?.toLowerCase()) {
      case "open":
        return (
          <Badge style={getBadgeStyle(primaryColor, 0.2)}>
            Open
          </Badge>
        );
      case "in-progress":
        return (
          <Badge style={getBadgeStyle(primaryColor, 0.15)}>
            In Progress
          </Badge>
        );
      case "closed":
        return (
          <Badge style={getBadgeStyle(primaryColor, 0.1, 0.7)}>
            Closed
          </Badge>
        );
      default:
        return (
          <Badge style={getBadgeStyle(primaryColor)}>
            {status}
          </Badge>
        );
    }
  };

  // Get badge for ticket priority
  const getPriorityBadge = (priority: string) => {
    switch (priority?.toLowerCase()) {
      case "low":
        return (
          <Badge style={getBadgeStyle(accentColor, 0.1)}>
            Low
          </Badge>
        );
      case "medium":
        return (
          <Badge style={getBadgeStyle(secondaryColor, 0.15)}>
            Medium
          </Badge>
        );
      case "high":
        return (
          <Badge style={getBadgeStyle(primaryColor, 0.2)}>
            High
          </Badge>
        );
      default:
        return (
          <Badge style={getBadgeStyle(primaryColor)}>
            {priority}
          </Badge>
        );
    }
  };

  // Loading state
  if (isLoading) {
    return (
      <AdminLayout>
        <div className="flex justify-center items-center h-96">
          <div className="text-center">
            <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            <p className="mt-2">Loading ticket...</p>
          </div>
        </div>
      </AdminLayout>
    );
  }

  // If ticket not found or data is not loaded yet
  if (!data) {
    return (
      <AdminLayout>
        <div className="flex justify-center items-center h-96">
          <div className="text-center">
            <p>Ticket not found or you don't have access.</p>
            <Button 
              variant="outline" 
              className="mt-4"
              onClick={() => navigate("/admin/tickets")}
            >
              Back to Tickets
            </Button>
          </div>
        </div>
      </AdminLayout>
    );
  }

  const { ticket, messages, server, department } = data;
  // Use the comprehensive color system with all three colors
  const brandColors = getBrandColors({
    primaryColor,
    secondaryColor,
    accentColor
  });

  return (
    <AdminLayout>
      {/* Page Header */}
      <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
        <div className="flex items-center">
          <Button 
            variant="outline" 
            size="icon"
            className="mr-4"
            onClick={() => navigate("/admin/tickets")}
          >
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div>
            <div className="flex items-center flex-wrap gap-2">
              <h1 className="text-2xl font-semibold">{ticket.subject}</h1>
              <div className="ml-1">{getStatusBadge(ticket.status)}</div>
              <div className="ml-1">{getPriorityBadge(ticket.priority)}</div>
              {department && (
                <Badge variant="outline" className="text-xs py-0 bg-primary/10">
                  {department.name}
                </Badge>
              )}
            </div>
            <div className="flex items-center text-gray-500 mt-1 text-sm">
              <span>Created on {format(new Date(ticket.createdAt), 'MMM d, yyyy')}</span>
              <span className="mx-2">•</span>
              <span>Last updated {format(new Date(ticket.updatedAt), 'MMM d, yyyy h:mm a')}</span>
              {ticket.user && (
                <>
                  <span className="mx-2">•</span>
                  <span>User: {ticket.user.fullName} ({ticket.user.email})</span>
                </>
              )}
            </div>
          </div>
        </div>
        <div className="mt-4 md:mt-0 flex gap-2 flex-wrap">
          {/* Change Status Dropdown */}
          <div className="flex gap-2">
            {/* Download button - always visible */}
            <Button
              variant="outline"
              className="flex items-center"
              onClick={() => window.open(`/api/tickets/${ticketId}/download`, '_blank')}
            >
              <Download className="h-4 w-4 mr-2" />
              Download PDF
            </Button>
            
            {/* Close button - only visible for open tickets */}
            {ticket.status !== "closed" && (
              <Button 
                variant="outline" 
                className="flex items-center"
                onClick={() => closeTicketMutation.mutate()}
                disabled={closeTicketMutation.isPending}
              >
                <Check className="h-4 w-4 mr-2" />
                {closeTicketMutation.isPending ? "Closing..." : "Close Ticket"}
              </Button>
            )}
            
            {/* Reopen button - only visible for closed tickets */}
            {ticket.status === "closed" && (
              <Button 
                variant="outline" 
                className="flex items-center"
                onClick={() => reopenTicketMutation.mutate()}
                disabled={reopenTicketMutation.isPending}
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                {reopenTicketMutation.isPending ? "Reopening..." : "Reopen Ticket"}
              </Button>
            )}
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {/* Main Chat Section */}
        <div className="md:col-span-2">
          <Card className="mb-6">
            <CardHeader className="py-3 px-4" style={getContainerStyle()}>
              <CardTitle className="text-md font-medium">Ticket Messages</CardTitle>
            </CardHeader>
            <CardContent className="p-4">
              {messages.length > 0 ? (
                <MessageList messages={messages} currentUserId={currentUser?.id || 0} />
              ) : (
                <div className="flex justify-center items-center h-32">
                  <p className="text-gray-500">No messages yet</p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Reply Section */}
          {ticket.status !== "closed" && (
            <Card className="mb-6">
              <CardHeader className="py-3 px-4" style={getContainerStyle()}>
                <CardTitle className="text-md font-medium">Reply</CardTitle>
              </CardHeader>
              <CardContent className="p-4">
                {/* AI Suggestion section */}
                {aiStatus?.enabled && (
                  <div className="mb-4">
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center">
                        <BrainCircuit className="h-4 w-4 mr-2" style={{ color: brandColors.primary.full }} />
                        <h3 className="text-sm font-medium">AI Assistant</h3>
                      </div>
                      <div 
                        className="flex items-center cursor-pointer text-xs hover:underline"
                        onClick={handleGenerateAISuggestion}
                        style={{ 
                          color: isGeneratingSuggestion ? brandColors.primary.medium : brandColors.primary.full,
                        }}
                      >
                        {isGeneratingSuggestion ? (
                          <>
                            <Loader2 className="h-3.5 w-3.5 mr-1.5 animate-spin" />
                            <span>Generating...</span>
                          </>
                        ) : (
                          <>
                            <Sparkles className="h-3.5 w-3.5 mr-1.5" />
                            <span>Generate with AI</span>
                          </>
                        )}
                      </div>
                    </div>
                    
                    {aiSuggestion && (
                      <div className="mb-4 mt-2">
                        <div 
                          className="border border-gray-200 rounded-md p-3 bg-gray-50 text-sm"
                          style={{ borderColor: brandColors.primary.lighter }}
                        >
                          <p className="whitespace-pre-wrap">{aiSuggestion}</p>
                        </div>
                        <div className="flex justify-end mt-2 space-x-2">
                          <Button 
                            variant="outline" 
                            size="sm"
                            className="text-xs"
                            onClick={handleClearSuggestion}
                          >
                            Clear
                          </Button>
                          <Button 
                            variant="outline" 
                            size="sm"
                            className="text-xs"
                            onClick={handleApplySuggestion}
                            style={{ 
                              borderColor: brandColors.primary.lighter,
                              color: brandColors.primary.full
                            }}
                          >
                            Use Suggestion
                          </Button>
                        </div>
                      </div>
                    )}
                  </div>
                )}
                
                {/* Message textarea */}
                <Textarea
                  value={message}
                  onChange={(e) => setMessage(e.target.value)}
                  placeholder="Type your reply here..."
                  className="min-h-[120px] mb-3"
                />
                
                <div className="flex justify-end">
                  <Button 
                    onClick={handleSendMessage}
                    disabled={addMessageMutation.isPending || !message.trim()}
                    style={{
                      backgroundColor: brandColors.primary.full,
                      color: 'white'
                    }}
                  >
                    {addMessageMutation.isPending ? (
                      <>
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" /> Sending...
                      </>
                    ) : (
                      <>
                        <Send className="h-4 w-4 mr-2" /> Send Reply
                      </>
                    )}
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Sidebar Information */}
        <div className="md:col-span-1 space-y-6">
          {/* User Information */}
          {ticket.user && (
            <Card>
              <CardHeader className="py-3 px-4" style={getContainerStyle()}>
                <CardTitle className="text-md font-medium">User Information</CardTitle>
              </CardHeader>
              <CardContent className="p-4">
                <div className="space-y-2">
                  <div>
                    <p className="text-sm font-medium">Name</p>
                    <p className="text-sm">{ticket.user.fullName}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium">Email</p>
                    <p className="text-sm">{ticket.user.email}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium">User ID</p>
                    <p className="text-sm">{ticket.userId}</p>
                  </div>
                  <div className="pt-2">
                    <Button 
                      variant="outline" 
                      className="w-full text-sm" 
                      onClick={() => navigate(`/admin/users/${ticket.userId}`)}
                    >
                      View User Profile
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* VPS Server Information */}
          {server && (
            <Card>
              <CardHeader className="py-3 px-4" style={getContainerStyle()}>
                <CardTitle className="text-md font-medium flex items-center">
                  <Server className="h-4 w-4 mr-2" style={{ color: brandColors.primary.full }} /> VPS Server
                </CardTitle>
              </CardHeader>
              <CardContent className="p-4">
                <div className="space-y-3">
                  <div>
                    <p className="text-sm font-medium">Name</p>
                    <p className="text-sm">{server.name}</p>
                  </div>
                  
                  <div>
                    <p className="text-sm font-medium">Status</p>
                    <div className="flex items-center mt-1">
                      {server.status === "online" || server.status === "running" ? (
                        <Badge 
                          className="text-xs px-2 py-0.5 bg-green-100 text-green-800 border border-green-200"
                        >
                          <Wifi className="h-3 w-3 mr-1" /> Online
                        </Badge>
                      ) : (
                        <Badge 
                          className="text-xs px-2 py-0.5 bg-red-100 text-red-800 border border-red-200"
                        >
                          <WifiOff className="h-3 w-3 mr-1" /> Offline
                        </Badge>
                      )}
                    </div>
                  </div>
                  
                  <div>
                    <p className="text-sm font-medium">IP Address</p>
                    <p className="text-sm font-mono">{server.ip}</p>
                  </div>
                  
                  {server.hostname && (
                    <div>
                      <p className="text-sm font-medium">Hostname</p>
                      <p className="text-sm font-mono">{server.hostname}</p>
                    </div>
                  )}
                  
                  <div className="pt-2">
                    <Button 
                      variant="outline" 
                      className="w-full text-sm"
                      onClick={() => navigate(`/admin/servers/${server.id}`)}
                    >
                      View Server Details
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Department Information */}
          {department && (
            <Card>
              <CardHeader className="py-3 px-4" style={getContainerStyle()}>
                <CardTitle className="text-md font-medium">Department</CardTitle>
              </CardHeader>
              <CardContent className="p-4">
                <div className="space-y-2">
                  <div>
                    <p className="text-sm font-medium">Name</p>
                    <p className="text-sm">{department.name}</p>
                  </div>
                  
                  {department.description && (
                    <div>
                      <p className="text-sm font-medium">Description</p>
                      <p className="text-sm">{department.description}</p>
                    </div>
                  )}
                  
                  <div>
                    <p className="text-sm font-medium">Requires VPS</p>
                    <p className="text-sm">{department.requiresVps ? "Yes" : "No"}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* VirtFusion Credits */}
          {userBalance?.data?.data?.credit && (
            <Card>
              <CardHeader className="py-3 px-4" style={getContainerStyle()}>
                <CardTitle className="text-md font-medium flex items-center">
                  <Coins className="h-4 w-4 mr-2" style={{ color: brandColors.accent.full }} /> User's VirtFusion Credits
                </CardTitle>
                <CardDescription className="text-xs mt-1">
                  100 tokens = $1.00 USD
                </CardDescription>
              </CardHeader>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <p className="text-sm"><span className="font-semibold">${((userBalance?.data?.data?.credit?.tokens ? parseFloat(userBalance.data.data.credit.tokens) : 0) / 100).toFixed(2)}</span> USD</p>
                  <p className="text-xs px-2 py-1 rounded-md" style={getBadgeStyle(accentColor, 0.1)}>
                    {userBalance?.data?.data?.credit?.tokens ? parseFloat(userBalance.data.data.credit.tokens).toLocaleString() : 0} tokens
                  </p>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </AdminLayout>
  );
}