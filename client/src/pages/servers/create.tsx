import { useState, useEffect } from "react";
import { useLocation } from "wouter";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import DashboardLayout from "@/components/layouts/DashboardLayout";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { useToast } from "@/hooks/use-toast";
import { PackageSelector, Package } from "@/components/servers/PackageSelector";
import { CloudConfiguration } from "@/components/servers/CloudConfiguration";
import {
  CloudPricingConfig,
  ServerConfiguration,
  getDefaultServerConfig,
  formatPrice,
  validateServerConfig
} from "@/lib/pricing";
import {
  ArrowLeft,
  Server,
  Package as PackageIcon,
  Cloud,
  InfoIcon,
  CheckCircle,
  AlertCircle
} from "lucide-react";

// Form validation schema
const serverCreationSchema = z.object({
  name: z.string()
    .min(3, "Server name must be at least 3 characters")
    .max(64, "Server name must be 64 characters or less")
    .regex(/^[a-zA-Z0-9 ._-]+$/, "Server name can only contain letters, numbers, spaces, and the characters ._-"),
  templateId: z.coerce.number().min(1, "Please select an operating system"),
  hypervisorId: z.coerce.number().min(1, "Please select a hypervisor").optional(),
  pricingMode: z.enum(["package", "cloud"]),
  packageId: z.coerce.number().optional(),
});

type ServerCreationFormValues = z.infer<typeof serverCreationSchema>;

interface Hypervisor {
  id: number;
  name: string;
  location?: string;
  enabled: boolean;
}

interface Template {
  id: number;
  name: string;
  description?: string;
  category?: string;
}

export default function ServerCreatePage() {
  const [, navigate] = useLocation();
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const [pricingMode, setPricingMode] = useState<"package" | "cloud">("package");
  const [selectedPackageId, setSelectedPackageId] = useState<number | null>(null);
  const [cloudConfig, setCloudConfig] = useState<ServerConfiguration>(getDefaultServerConfig());
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Fetch packages
  const { data: packages = [], isLoading: packagesLoading } = useQuery<Package[]>({
    queryKey: ["/api/packages"],
    staleTime: 60 * 1000,
  });

  // Fetch cloud pricing configuration
  const { data: cloudPricing, isLoading: cloudPricingLoading } = useQuery<CloudPricingConfig>({
    queryKey: ["/api/cloud-pricing"],
    staleTime: 60 * 1000,
  });

  // Fetch hypervisors
  const { data: hypervisors = [], isLoading: hypervisorsLoading } = useQuery<Hypervisor[]>({
    queryKey: ["/api/hypervisors"],
    staleTime: 60 * 1000,
  });

  // Fetch OS templates for selected package
  const { data: templates = [], isLoading: templatesLoading } = useQuery<Template[]>({
    queryKey: ["/api/packages", selectedPackageId, "templates"],
    enabled: pricingMode === "package" && !!selectedPackageId,
    staleTime: 60 * 1000,
  });

  // Fetch all templates for cloud mode
  const { data: allTemplatesResponse, isLoading: allTemplatesLoading } = useQuery<{data: Template[]}>({
    queryKey: ["/api/admin/all-templates"],
    enabled: pricingMode === "cloud",
    staleTime: 60 * 1000,
  });

  const allTemplates = allTemplatesResponse?.data || [];

  // Get user's credit balance
  const { data: balanceResponse } = useQuery<{credits: number}>({
    queryKey: ["/api/billing/balance"],
    staleTime: 30 * 1000,
  });

  const userCredits = balanceResponse?.credits || 0;

  // Form setup
  const form = useForm<ServerCreationFormValues>({
    resolver: zodResolver(serverCreationSchema),
    defaultValues: {
      name: "",
      templateId: 0,
      hypervisorId: 0,
      pricingMode: "package",
      packageId: 0,
    },
  });

  // Update form when pricing mode changes
  useEffect(() => {
    form.setValue("pricingMode", pricingMode);
    if (pricingMode === "package") {
      form.setValue("packageId", selectedPackageId || 0);
    }
  }, [pricingMode, selectedPackageId, form]);

  // Calculate estimated cost
  const getEstimatedCost = (): number => {
    if (pricingMode === "package" && selectedPackageId) {
      const selectedPackage = packages.find(p => p.id === selectedPackageId);
      return selectedPackage?.pricing?.price || 0;
    } else if (pricingMode === "cloud" && cloudPricing) {
      const { calculateCloudPricing } = require("@/lib/pricing");
      const breakdown = calculateCloudPricing(cloudConfig, cloudPricing);
      return breakdown.total;
    }
    return 0;
  };

  const estimatedCost = getEstimatedCost();

  // Server creation mutation
  const createServerMutation = useMutation({
    mutationFn: async (data: any) => {
      const response = await fetch("/api/servers", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Failed to create server");
      }

      return response.json();
    },
    onSuccess: (data) => {
      toast({
        title: "Server created successfully!",
        description: `Your server "${data.name || 'New Server'}" is being deployed.`,
      });
      queryClient.invalidateQueries({ queryKey: ["/api/user/servers"] });
      navigate("/servers");
    },
    onError: (error: Error) => {
      toast({
        title: "Failed to create server",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Form submission
  const onSubmit = async (values: ServerCreationFormValues) => {
    if (estimatedCost > userCredits) {
      toast({
        title: "Insufficient credits",
        description: "Please add more credits to create this server.",
        variant: "destructive",
      });
      return;
    }

    setIsSubmitting(true);

    try {
      const serverData: any = {
        name: values.name,
        templateId: values.templateId,
        configurationType: pricingMode === "cloud" ? "custom" : "package",
      };

      if (pricingMode === "package") {
        serverData.packageId = selectedPackageId;
      } else {
        // Cloud configuration
        serverData.cpuCores = cloudConfig.cpuCores;
        serverData.memory = cloudConfig.memory;
        serverData.storage = cloudConfig.storage;
        serverData.networkSpeedInbound = cloudConfig.networkSpeed;
        serverData.networkSpeedOutbound = cloudConfig.networkSpeed;
        serverData.natIpv4 = cloudConfig.natIpv4;
        serverData.publicIpv4 = cloudConfig.publicIpv4;
        serverData.additionalIpv6 = cloudConfig.additionalIpv6;
      }

      if (values.hypervisorId) {
        serverData.hypervisorId = values.hypervisorId;
      }

      await createServerMutation.mutateAsync(serverData);
    } finally {
      setIsSubmitting(false);
    }
  };

  const isFormValid = () => {
    const values = form.getValues();
    if (!values.name || !values.templateId) return false;

    if (pricingMode === "package" && !selectedPackageId) return false;
    if (pricingMode === "cloud") {
      const errors = validateServerConfig(cloudConfig);
      if (errors.length > 0) return false;
    }

    return estimatedCost <= userCredits;
  };

  return (
    <DashboardLayout>
      <div className="container mx-auto px-4 py-8 max-w-6xl">
        {/* Header */}
        <div className="flex items-center gap-4 mb-8">
          <Button variant="outline" size="sm" onClick={() => navigate("/servers")}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Servers
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Create New Server</h1>
            <p className="text-muted-foreground">
              Deploy a new virtual server with your preferred configuration
            </p>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Configuration */}
          <div className="lg:col-span-2 space-y-6">
            {/* Pricing Mode Selection */}
            <Card>
              <CardHeader>
                <CardTitle>Pricing Model</CardTitle>
                <CardDescription>
                  Choose between pre-configured packages or custom cloud pricing
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Tabs value={pricingMode} onValueChange={(value) => setPricingMode(value as "package" | "cloud")}>
                  <TabsList className="grid w-full grid-cols-2">
                    <TabsTrigger value="package" className="flex items-center gap-2">
                      <PackageIcon className="h-4 w-4" />
                      Packages
                    </TabsTrigger>
                    <TabsTrigger value="cloud" className="flex items-center gap-2">
                      <Cloud className="h-4 w-4" />
                      Cloud Pricing
                    </TabsTrigger>
                  </TabsList>
                </Tabs>
              </CardContent>
            </Card>

            {/* Package Selection */}
            {pricingMode === "package" && (
              <Card>
                <CardHeader>
                  <CardTitle>Select Package</CardTitle>
                  <CardDescription>
                    Choose from our pre-configured server packages
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <PackageSelector
                    packages={packages}
                    selectedPackageId={selectedPackageId}
                    onPackageSelect={setSelectedPackageId}
                    isLoading={packagesLoading}
                  />
                </CardContent>
              </Card>
            )}

            {/* Cloud Configuration */}
            {pricingMode === "cloud" && cloudPricing && (
              <CloudConfiguration
                configuration={cloudConfig}
                onConfigurationChange={setCloudConfig}
                pricingConfig={cloudPricing}
              />
            )}
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Server Details Form */}
            <Card>
              <CardHeader>
                <CardTitle>Server Details</CardTitle>
                <CardDescription>
                  Configure your server settings
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Form {...form}>
                  <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                    {/* Server Name */}
                    <FormField
                      control={form.control}
                      name="name"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Server Name</FormLabel>
                          <FormControl>
                            <Input placeholder="My Web Server" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {/* Operating System */}
                    <FormField
                      control={form.control}
                      name="templateId"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Operating System</FormLabel>
                          <Select onValueChange={field.onChange} value={field.value?.toString()}>
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select OS template" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {(pricingMode === "package" ? templates : allTemplates).map((template) => (
                                <SelectItem key={template.id} value={template.id.toString()}>
                                  {template.name}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {/* Hypervisor (optional) */}
                    <FormField
                      control={form.control}
                      name="hypervisorId"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Location (Optional)</FormLabel>
                          <Select onValueChange={field.onChange} value={field.value?.toString()}>
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Auto-select location" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {hypervisors.filter(h => h.enabled).map((hypervisor) => (
                                <SelectItem key={hypervisor.id} value={hypervisor.id.toString()}>
                                  {hypervisor.name}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </form>
                </Form>
              </CardContent>
            </Card>

            {/* Cost Summary */}
            <Card>
              <CardHeader>
                <CardTitle>Cost Summary</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-muted-foreground">Estimated Monthly Cost</span>
                    <span className="text-lg font-semibold">{formatPrice(estimatedCost)}</span>
                  </div>

                  <Separator />

                  <div className="flex justify-between items-center">
                    <span className="text-sm text-muted-foreground">Your Credits</span>
                    <span className={`text-sm font-medium ${userCredits >= estimatedCost ? 'text-green-600' : 'text-red-600'}`}>
                      {formatPrice(userCredits)}
                    </span>
                  </div>

                  {userCredits < estimatedCost && (
                    <Alert variant="destructive">
                      <AlertCircle className="h-4 w-4" />
                      <AlertTitle>Insufficient Credits</AlertTitle>
                      <AlertDescription>
                        You need {formatPrice(estimatedCost - userCredits)} more credits to create this server.
                      </AlertDescription>
                    </Alert>
                  )}

                  <Button
                    type="submit"
                    className="w-full"
                    disabled={!isFormValid() || isSubmitting}
                    onClick={form.handleSubmit(onSubmit)}
                  >
                    {isSubmitting ? (
                      <>
                        <Server className="mr-2 h-4 w-4 animate-spin" />
                        Creating Server...
                      </>
                    ) : (
                      <>
                        <Server className="mr-2 h-4 w-4" />
                        Create Server
                      </>
                    )}
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <InfoIcon className="h-4 w-4" />
                  Information
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2 text-sm text-muted-foreground">
                  <p>• Server deployment typically takes 2-5 minutes</p>
                  <p>• You will receive an email with login credentials</p>
                  <p>• Credits are charged monthly for active servers</p>
                  <p>• You can modify resources after deployment</p>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}
