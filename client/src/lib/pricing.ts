/**
 * Pricing utilities for server creation
 */

export interface CloudPricingConfig {
  cpuPricePerCore: number;
  ramPricePerGB: number;
  storagePricePerGB: number;
  networkPricePerMbps: number;
  natIpv4Price: number;
  publicIpv4Price: number;
  publicIpv6Price: number;
  // Enable/disable flags
  cpuPricingEnabled: boolean;
  ramPricingEnabled: boolean;
  storagePricingEnabled: boolean;
  networkPricingEnabled: boolean;
  natIpv4PricingEnabled: boolean;
  publicIpv4PricingEnabled: boolean;
  publicIpv6PricingEnabled: boolean;
}

export interface ServerConfiguration {
  cpuCores: number;
  memory: number; // in MB
  storage: number; // in GB
  networkSpeed: number; // in Mbps
  natIpv4: boolean;
  publicIpv4: boolean;
  additionalIpv6: number;
}

export interface PricingBreakdown {
  cpu: number;
  memory: number;
  storage: number;
  network: number;
  natIpv4: number;
  publicIpv4: number;
  publicIpv6: number;
  total: number;
}

/**
 * Calculate cloud-based pricing for a server configuration
 */
export function calculateCloudPricing(
  config: ServerConfiguration,
  pricing: CloudPricingConfig
): PricingBreakdown {
  const hoursPerMonth = 720; // 24 * 30
  
  const breakdown: PricingBreakdown = {
    cpu: 0,
    memory: 0,
    storage: 0,
    network: 0,
    natIpv4: 0,
    publicIpv4: 0,
    publicIpv6: 0,
    total: 0,
  };

  // CPU pricing
  if (pricing.cpuPricingEnabled) {
    breakdown.cpu = config.cpuCores * pricing.cpuPricePerCore * hoursPerMonth;
  }

  // Memory pricing (convert MB to GB)
  if (pricing.ramPricingEnabled) {
    breakdown.memory = (config.memory / 1024) * pricing.ramPricePerGB * hoursPerMonth;
  }

  // Storage pricing
  if (pricing.storagePricingEnabled) {
    breakdown.storage = config.storage * pricing.storagePricePerGB * hoursPerMonth;
  }

  // Network pricing
  if (pricing.networkPricingEnabled) {
    breakdown.network = config.networkSpeed * pricing.networkPricePerMbps * hoursPerMonth;
  }

  // NAT IPv4 pricing
  if (pricing.natIpv4PricingEnabled && config.natIpv4) {
    breakdown.natIpv4 = pricing.natIpv4Price * hoursPerMonth;
  }

  // Public IPv4 pricing
  if (pricing.publicIpv4PricingEnabled && config.publicIpv4) {
    breakdown.publicIpv4 = pricing.publicIpv4Price * hoursPerMonth;
  }

  // Public IPv6 pricing
  if (pricing.publicIpv6PricingEnabled && config.additionalIpv6 > 0) {
    breakdown.publicIpv6 = config.additionalIpv6 * pricing.publicIpv6Price * hoursPerMonth;
  }

  // Calculate total
  breakdown.total = 
    breakdown.cpu +
    breakdown.memory +
    breakdown.storage +
    breakdown.network +
    breakdown.natIpv4 +
    breakdown.publicIpv4 +
    breakdown.publicIpv6;

  return breakdown;
}

/**
 * Format price for display
 */
export function formatPrice(price: number): string {
  return `$${price.toFixed(2)}`;
}

/**
 * Format resource values for display
 */
export function formatResource(value: number, unit: string): string {
  if (unit === 'GB' && value >= 1024) {
    return `${(value / 1024).toFixed(1)} TB`;
  }
  return `${value} ${unit}`;
}

/**
 * Get default server configuration
 */
export function getDefaultServerConfig(): ServerConfiguration {
  return {
    cpuCores: 1,
    memory: 1024, // 1 GB
    storage: 25, // 25 GB
    networkSpeed: 100, // 100 Mbps
    natIpv4: false,
    publicIpv4: true,
    additionalIpv6: 0,
  };
}

/**
 * Validate server configuration
 */
export function validateServerConfig(config: ServerConfiguration): string[] {
  const errors: string[] = [];

  if (config.cpuCores < 1) {
    errors.push('CPU cores must be at least 1');
  }
  if (config.cpuCores > 32) {
    errors.push('CPU cores cannot exceed 32');
  }

  if (config.memory < 512) {
    errors.push('Memory must be at least 512 MB');
  }
  if (config.memory > 131072) { // 128 GB
    errors.push('Memory cannot exceed 128 GB');
  }

  if (config.storage < 10) {
    errors.push('Storage must be at least 10 GB');
  }
  if (config.storage > 2048) { // 2 TB
    errors.push('Storage cannot exceed 2 TB');
  }

  if (config.networkSpeed < 10) {
    errors.push('Network speed must be at least 10 Mbps');
  }
  if (config.networkSpeed > 10000) { // 10 Gbps
    errors.push('Network speed cannot exceed 10 Gbps');
  }

  if (config.additionalIpv6 < 0) {
    errors.push('Additional IPv6 addresses cannot be negative');
  }
  if (config.additionalIpv6 > 100) {
    errors.push('Additional IPv6 addresses cannot exceed 100');
  }

  return errors;
}
