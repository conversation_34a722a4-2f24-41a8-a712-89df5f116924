# 🚀 SkyPANEL - Enterprise VirtFusion Management Platform
<div align="center">
  <h3>🌟 Next-Generation VPS Hosting Control Panel 🌟</h3>
  <p><strong>Complete VirtFusion integration with AI-powered support, real-time monitoring, and advanced automation</strong></p>

  <div align="center">

  [![GitHub Stars](https://img.shields.io/github/stars/skyvps360/SkyPANEL?style=social)](https://github.com/skyvps360/SkyPANEL)
  [![Discord](https://img.shields.io/discord/1310474963865833483?style=social&logo=discord)](https://skyvps360.xyz/discord)

  </div>

  ![TypeScript](https://img.shields.io/badge/TypeScript-3178C6?style=for-the-badge&logo=typescript&logoColor=white)
  ![React](https://img.shields.io/badge/React-61DAFB?style=for-the-badge&logo=react&logoColor=black)
  ![Node.js](https://img.shields.io/badge/Node.js-339933?style=for-the-badge&logo=nodedotjs&logoColor=white)
  ![Express](https://img.shields.io/badge/Express-000000?style=for-the-badge&logo=express&logoColor=white)
  ![PostgreSQL](https://img.shields.io/badge/PostgreSQL-4169E1?style=for-the-badge&logo=postgresql&logoColor=white)
  ![Tailwind CSS](https://img.shields.io/badge/Tailwind_CSS-06B6D4?style=for-the-badge&logo=tailwind-css&logoColor=white)
  ![Discord](https://img.shields.io/badge/Discord-5865F2?style=for-the-badge&logo=discord&logoColor=white)
  ![Google AI](https://img.shields.io/badge/Google_AI-4285F4?style=for-the-badge&logo=google&logoColor=white)
  ![VirtFusion](https://img.shields.io/badge/VirtFusion-FF6B35?style=for-the-badge&logo=server&logoColor=white)
</div>

## 💝 Support My Work
If you find my work helpful, consider supporting me:

[![PayPal](https://www.paypalobjects.com/en_US/i/btn/btn_donateCC_LG.gif)](https://www.paypal.com/donate/?hosted_button_id=TEY7YEJC8X5HW)

---

## 📋 Table of Contents

- [Overview](#-overview)
- [Tech Stack](#-tech-stack)
- [Key Features](#-key-features)
- [Complete API Documentation](#-complete-api-documentation)
- [Environment Setup](#-environment-setup)
- [Installation & Development](#-installation--development)
- [VirtFusion Integration Guide](#-virtfusion-integration-guide)
- [Brand Theming System](#-brand-theming-system)
- [Discord Integration](#-discord-integration)
- [AI-Powered Support](#-ai-powered-support)
- [VNC Console](#-vnc-console)
- [Billing & Transaction System](#-billing--transaction-system)
- [User Management](#-user-management)
- [Content Management](#-content-management)
- [Monitoring & Analytics](#-monitoring--analytics)
- [API Key Management](#-api-key-management)
- [Maintenance Mode](#-maintenance-mode)
- [Security Features](#-security-features)
- [Troubleshooting](#-troubleshooting)
- [Recent Updates](#-recent-updates)

---

## 🔍 Overview

**SkyPANEL** is a cutting-edge, enterprise-grade VirtFusion client portal that revolutionizes VPS hosting management. Built with modern technologies and designed for scalability, it provides a comprehensive solution for hosting providers and their customers.

### 🎯 What Makes SkyPANEL Special

- **🤖 AI-Powered Support**: Integrated Google Gemini 2.5 Flash for intelligent customer support and automated responses
- **🔄 Real-Time Monitoring**: BetterStack integration for live infrastructure monitoring and status reporting
- **💬 Discord Integration**: Full two-way communication with Discord bot for ticket management and platform status
- **🎨 Dynamic Theming**: Advanced brand customization with multi-color theming system
- **🔐 Enterprise Security**: API key management, role-based access control, and secure authentication
- **📊 Advanced Analytics**: Comprehensive reporting, transaction tracking, and usage monitoring
- **🌐 VNC Console**: Built-in VNC client for direct server access and management
- **⚡ Modern Architecture**: React/TypeScript frontend with Node.js/Express backend and PostgreSQL database
- **🔌 Complete VirtFusion Integration**: Direct API integration for seamless VPS management

### 🏢 Perfect For

- **Hosting Providers**: Complete client management solution with billing and support
- **VPS Resellers**: White-label ready platform with custom branding capabilities
- **Enterprise team**: Advanced user management and monitoring tools
- **Service Providers**: Integrated support system with AI assistance and Discord integration

---

## 🛠️ Tech Stack

### Frontend
- **React 18**: Modern UI library with hooks and concurrent features
- **TypeScript**: Type-safe JavaScript for robust development
- **Vite**: Next-generation frontend build tool with HMR
- **TailwindCSS**: Utility-first CSS framework for rapid styling
- **Shadcn/UI**: High-quality UI components built on Radix UI
- **Radix UI**: Unstyled, accessible component primitives
- **React Query (TanStack Query)**: Powerful data fetching and state management
- **React Hook Form**: Performant forms with easy validation
- **Wouter**: Lightweight routing solution for SPAs
- **Framer Motion**: Production-ready motion library for React
- **Recharts**: Composable charting library built on D3
- **React Markdown**: Markdown rendering with syntax highlighting
- **React Leaflet**: Interactive maps for datacenter visualization
- **NoVNC**: Web-based VNC client for server console access

### Backend
- **Node.js**: JavaScript runtime for server-side development
- **Express**: Fast, unopinionated web framework
- **TypeScript**: Type-safe server-side development
- **PostgreSQL**: Advanced relational database with JSON support
- **Drizzle ORM**: Type-safe SQL ORM with excellent TypeScript integration
- **Zod**: TypeScript-first schema validation
- **Passport.js**: Authentication middleware with local strategy
- **Express Session**: Session management with PostgreSQL store
- **Bcrypt**: Password hashing and security
- **SMTP2GO**: Reliable email delivery service
- **Discord.js**: Discord bot integration and webhook support
- **PDFKit**: PDF generation for transaction exports and reports

### AI & Integrations
- **Google Gemini 2.5 Flash**: Advanced AI for customer support
- **VirtFusion API**: Complete VPS management integration
- **BetterStack**: Infrastructure monitoring and alerting
- **PayPal SDK**: Payment processing and billing
- **SendGrid**: Email delivery and templates

### DevOps & Tooling
- **ESBuild**: Fast JavaScript bundler for production
- **Drizzle Kit**: Database schema migration and management
- **Cross-env**: Cross-platform environment variables
- **TSX**: TypeScript execution for development
- **Husky**: Git hooks for code quality

---

## ✨ Key Features

### 👥 User Management
- **Secure Authentication**: Email verification, password reset, and account recovery
- **VirtFusion Sync**: Automatic synchronization with VirtFusion user accounts
- **Profile Management**: Real-time updates synced across platforms
- **Role-Based Access**: Admin and user roles with granular permissions
- **API Key Management**: Personal API keys with scoped permissions

### 💳 Billing System
- **Credit-Based Billing**: Flexible credit system with real-time balance tracking
- **Transaction Management**: Detailed transaction history with PDF exports
- **Transaction Exports**: PDF export functionality for transaction records
- **PayPal Integration**: Secure payment processing with webhook validation
- **Usage Monitoring**: Real-time VirtFusion resource usage tracking
- **Admin Controls**: Manual credit adjustments and transaction management

### 🤝 VirtFusion Integration
- **Direct API Integration**: Complete VirtFusion API integration for all operations
- **Real-Time Sync**: Live server status, resource usage, and billing data
- **User Management**: Automatic user creation and synchronization
- **Server Control**: Power management, password resets, and console access
- **Resource Monitoring**: Live CPU, memory, disk, and network statistics
- **Billing Integration**: Automatic credit deduction based on usage

### 🎫 Support System
- **Ticket Management**: Full-featured support ticket system with departments
- **Threaded Conversations**: Message threading with file attachments
- **Status Tracking**: Open, closed, and priority level management
- **Admin Assignment**: Ticket routing and bulk operations
- **Discord Integration**: Two-way sync with Discord threads
- **AI Assistance**: Google Gemini AI for intelligent response suggestions

### 📊 Admin Dashboard
- **User Management**: Comprehensive user administration with VirtFusion sync
- **Server Management**: Direct VirtFusion server control and monitoring
- **Financial Reporting**: Transaction tracking and revenue analytics
- **System Configuration**: Dynamic settings and branding management
- **Content Management**: Blog, documentation, and FAQ administration
- **Monitoring Tools**: Real-time system health and performance metrics

### 🔔 Notifications
- **Email Notifications**: Automated emails for account events and transactions
- **Discord Webhooks**: Real-time notifications to Discord channels
- **Transaction Alerts**: Payment confirmations and billing updates
- **Support Updates**: Ticket status changes and new message alerts
- **System Alerts**: Maintenance mode and service status notifications

---

## 🔌 Complete API Documentation

SkyPANEL provides a comprehensive REST API with over 100 endpoints for complete platform integration. All endpoints support JSON request/response format with proper error handling and validation.

### 🔐 Authentication & Authorization

#### API Key Authentication
```bash
# Include API key in Authorization header
Authorization: Bearer your_api_key_here
```

#### Available Scopes
- `read:user` - Read user profile information
- `read:servers` - Read server information and status
- `write:servers` - Create, update, or delete servers
- `read:billing` - View billing information and transactions
- `read:tickets` - View support tickets
- `write:tickets` - Create and update support tickets
- `admin:users` - Administrative access to user accounts (admin only)
- `admin:billing` - Administrative access to billing functions (admin only)
- `admin:system` - Administrative access to system settings (admin only)

#### Rate Limiting
- **100 requests per minute** per API key
- **429 Too Many Requests** response when exceeded
- Rate limit headers included in all responses

### 👤 User & Authentication Endpoints

#### User Management
```bash
GET    /api/user                           # Get current user profile
PATCH  /api/user                           # Update user profile
GET    /api/user/notifications              # Get user notifications
PATCH  /api/user/notifications/:id/read    # Mark notification as read
```

#### Authentication
```bash
POST   /api/auth/login                     # Authenticate user
POST   /api/auth/register                  # Create new account
POST   /api/auth/logout                    # End session
POST   /api/auth/forgot-password           # Request password reset
POST   /api/auth/reset-password            # Reset password with token
POST   /api/auth/change-password           # Change password (authenticated)
GET    /api/verification-status            # Check email verification
POST   /api/verification/verify-email      # Verify email with code
POST   /api/verification/resend            # Resend verification email
```

### 🖥️ Server Management Endpoints

#### User Server Operations
```bash
GET    /api/user/servers                   # List user's servers (paginated)
GET    /api/user/servers/:id               # Get server details
POST   /api/user/servers/:id/reset-password # Reset server password
POST   /api/user/servers/:id/power/:action # Power control (boot/shutdown/restart/poweroff)
GET    /api/user/servers/:id/vnc           # Get VNC status (toggles state)
GET    /api/user/servers/:id/traffic       # Get traffic statistics
GET    /api/user/servers/:id/logs          # Get server action logs
```

#### Server Packages & Plans
```bash
GET    /api/server-packages                # Get available server packages
GET    /api/plan-features                  # Get plan features for public display
GET    /api/datacenter-locations           # Get datacenter locations
```

### 💰 Billing & Transaction Endpoints

#### User Billing
```bash
GET    /api/billing/balance                # Get account balance
GET    /api/billing/usage/last30days       # Get 30-day usage statistics
POST   /api/billing/add-credits            # Add credits to account
GET    /api/transactions                   # List user transactions
GET    /api/transactions/:id               # Get transaction details
GET    /api/transactions/export            # Export transactions as PDF

```

#### PayPal Integration
```bash
POST   /api/billing/capture-paypal-payment # Process PayPal payment
```

### 🎫 Support System Endpoints

#### Ticket Management
```bash
GET    /api/tickets                        # List tickets (paginated)
POST   /api/tickets                        # Create new ticket
GET    /api/tickets/:id                    # Get ticket details
POST   /api/tickets/:id/messages           # Add message to ticket
POST   /api/tickets/:id/close              # Close ticket
POST   /api/tickets/:id/reopen             # Reopen ticket
GET    /api/tickets/:id/download           # Download ticket as PDF
GET    /api/ticket-departments             # Get available departments
```

### 🔑 API Key Management Endpoints

#### Personal API Keys
```bash
GET    /api/user/api-keys                  # List user's API keys
POST   /api/user/api-keys                  # Create new API key
DELETE /api/user/api-keys/:id              # Delete API key
```

### 🛠️ Admin Endpoints (Admin Access Required)

#### User Administration
```bash
GET    /api/admin/users                    # List all users
GET    /api/admin/users/:id                # Get user details
PATCH  /api/admin/users/:id                # Update user
DELETE /api/admin/users/:id                # Delete user
POST   /api/admin/users/:id/reset-password # Reset user password
PATCH  /api/admin/users/:id/status         # Enable/disable account
```

#### Server Administration
```bash
GET    /api/admin/servers                  # List all servers
GET    /api/admin/servers/:id              # Get server details
POST   /api/admin/servers                  # Create server
DELETE /api/admin/servers/:id              # Delete server
POST   /api/admin/servers/:id/power/:action # Server power control
POST   /api/admin/servers/:id/suspend      # Suspend server
POST   /api/admin/servers/:id/unsuspend    # Unsuspend server
GET    /api/admin/servers/:id/vnc          # Get VNC status
POST   /api/admin/servers/:id/vnc/enable   # Enable VNC
POST   /api/admin/servers/:id/vnc/disable  # Disable VNC
```

#### Billing Administration
```bash
GET    /api/admin/transactions             # List all transactions

POST   /api/credits                        # Add credits to user
DELETE /api/credits/:id                    # Cancel credit transaction
```

#### Support Administration
```bash
GET    /api/admin/tickets                  # List all tickets (paginated)
DELETE /api/admin/tickets/:id              # Delete ticket
```

#### System Administration
```bash
GET    /api/admin/settings                 # Get system settings
POST   /api/admin/settings                 # Update system settings
GET    /api/admin/platform-stats           # Get platform statistics
GET    /api/admin/services/status          # Get service status
POST   /api/admin/maintenance/enable       # Enable maintenance mode
POST   /api/admin/maintenance/disable      # Disable maintenance mode
GET    /api/admin/maintenance/generate-token # Generate bypass token
```

#### Content Management
```bash
GET    /api/admin/blog                     # List blog posts
POST   /api/admin/blog                     # Create blog post
PATCH  /api/admin/blog/:id                 # Update blog post
DELETE /api/admin/blog/:id                 # Delete blog post
GET    /api/admin/faqs                     # List FAQs
POST   /api/admin/faqs                     # Create FAQ
PUT    /api/admin/faqs/:id                 # Update FAQ
DELETE /api/admin/faqs/:id                 # Delete FAQ
GET    /api/admin/plan-features            # List plan features
POST   /api/admin/plan-features            # Create plan feature
PUT    /api/admin/plan-features/:id        # Update plan feature
DELETE /api/admin/plan-features/:id        # Delete plan feature
GET    /api/admin/datacenter-locations     # List datacenter locations
POST   /api/admin/datacenter-locations     # Create location
PUT    /api/admin/datacenter-locations/:id # Update location
DELETE /api/admin/datacenter-locations/:id # Delete location
```

### 🌐 Public Endpoints (No Authentication Required)

#### Public Information
```bash
GET    /api/public/service-status          # Get service status
GET    /api/public/platform-stats          # Get platform statistics
GET    /api/public/blog                    # Get published blog posts
GET    /api/public/blog/:slug              # Get blog post by slug
GET    /api/public/docs                    # Get documentation
GET    /api/public/packages                # Get available server packages
GET    /api/settings/public                # Get public settings
GET    /api/settings/branding              # Get branding information
```

### 🔧 Maintenance Mode Endpoints

#### Maintenance Management
```bash
GET    /api/maintenance/status             # Get maintenance status
POST   /api/maintenance/toggle             # Enable/disable maintenance mode (admin only)
GET    /api/maintenance/token              # Get maintenance bypass token (admin only)
POST   /api/maintenance/token/regenerate   # Regenerate bypass token (admin only)
POST   /api/maintenance/token/validate     # Validate bypass token
```

### 🔐 Protected Routes (Authentication Required)

#### User Profile Management
```bash
GET    /api/user                           # Get current user profile
PATCH  /api/user/profile                   # Update user profile
GET    /api/verification-status            # Check email verification status
```

#### Password & Authentication Management
```bash
POST   /api/auth/request-password-reset    # Request password reset via email
POST   /api/auth/verify-reset-code         # Verify password reset code
POST   /api/auth/request-username          # Request username reminder via email
POST   /api/auth/change-password           # Change password (authenticated users)
```

#### Server Management (User Access)
```bash
GET    /api/user/servers                   # List user's servers (paginated)
GET    /api/user/servers/:id               # Get specific server details
POST   /api/user/servers/:id/reset-password # Reset server password
POST   /api/user/servers/:id/power/:action # Server power control (boot/shutdown/restart/poweroff)
GET    /api/user/servers/:id/vnc           # Get/toggle VNC status
GET    /api/user/servers/:id/traffic       # Get server traffic statistics
GET    /api/user/servers/:id/logs          # Get server action logs
```

#### Billing & Financial Management
```bash
GET    /api/transactions                   # List user transactions
GET    /api/transactions/:id               # Get specific transaction details
GET    /api/transactions/export            # Export transactions as PDF
GET    /api/transactions/:id/download      # Download transaction receipt as PDF

```

#### Resource Management
```bash
GET    /api/resource-packs                 # Get available resource packs (returns empty - VirtFusion integration)
GET    /api/resource-packs/:id             # Get specific resource pack (not implemented)
GET    /api/ip-addresses                   # Get available IP addresses from VirtFusion
GET    /api/storage-volumes                # Get storage volumes (returns empty - VirtFusion integration)
```

### 🛡️ Admin-Only Endpoints (Admin Role Required)

#### Package & Pricing Management
```bash
GET    /api/admin/packages                 # Get all VirtFusion packages with pricing
POST   /api/admin/packages/:id/pricing     # Create/update package pricing
DELETE /api/admin/packages/:id/pricing     # Delete package pricing
GET    /api/admin/hypervisors              # Get hypervisor groups for server creation
```

#### Credit Management
```bash
POST   /api/credits                        # Add credits to user account
DELETE /api/credits/:id                    # Cancel/reverse credit transaction
```

#### Testing & Development
```bash
POST   /api/test-virtfusion-user           # Test VirtFusion user creation (admin only)
```

#### Resource Pack Management (VirtFusion Integration)
```bash
POST   /api/resource-packs                 # Create resource pack (not implemented)
DELETE /api/resource-packs/:id             # Delete resource pack (not implemented)
POST   /api/resource-packs/:id/servers     # Manage servers in resource pack (not implemented)
```

#### Email System Administration
```bash
GET    /api/admin/email-logs               # List email logs with filtering
GET    /api/admin/email-logs/:id           # Get specific email log details
```

#### Package Template Management
```bash
GET    /api/admin/packages/:packageId/templates # Get OS templates for specific package
```

### 🔌 API-Only Routes (API Key Authentication Only)

These routes are exclusively for API key authentication and cannot be accessed via web sessions:

#### API-Only User Information
```bash
GET    /api/me                             # Get current user info (API key only)
GET    /api/servers                        # Get user servers (API key only)
GET    /api/balance                        # Get user credit balance (API key only)
```

### 🔌 V1 API Routes (Versioned API)

The V1 API provides versioned endpoints with enhanced functionality:

#### V1 User & Billing
```bash
GET    /api/v1/me                          # Get current user information (enhanced)
GET    /api/v1/balance                     # Get user credit balance (enhanced)
```

### 📊 Pagination Support

Many endpoints support pagination with the following parameters:
```bash
?page=1&limit=20                          # Page number and items per page
```

Response format for paginated endpoints:
```json
{
  "data": [...],
  "pagination": {
    "currentPage": 1,
    "totalPages": 5,
    "totalItems": 100,
    "itemsPerPage": 20
  }
}
```

### ⚠️ Error Handling

All API endpoints return consistent error responses:

#### HTTP Status Codes
- `200` - Success
- `400` - Bad Request (validation errors)
- `401` - Unauthorized (authentication required)
- `403` - Forbidden (insufficient permissions)
- `404` - Not Found
- `429` - Too Many Requests (rate limited)
- `500` - Internal Server Error

#### Error Response Format
```json
{
  "error": {
    "code": "invalid_scope",
    "message": "The API key does not have the required scope",
    "required_scope": "write:servers"
  }
}
```

---

## 🔧 Environment Setup

Create a `.env` file with the following variables:

```bash
# Database Configuration
DATABASE_URL=postgres://username:password@hostname:port/database

# Session Management
SESSION_SECRET=your_secure_random_string_here

# VirtFusion API Integration
VIRTFUSION_API_URL=https://your-virtfusion.com/api/v1
VIRTFUSION_API_KEY=your_virtfusion_api_key

# Email Configuration (SMTP2GO)
SMTP2GO_API_KEY=your_smtp2go_api_key
SMTP_FROM=<EMAIL>
SMTP_FROM_NAME=Your Company Support

# Discord Integration (Optional)
DISCORD_WEBHOOK_URL=https://discord.com/api/webhooks/your-webhook-url
DISCORD_ROLE_ID=your_discord_role_id

# Discord Bot Integration (Optional)
DISCORD_BOT_TOKEN=your_discord_bot_token
DISCORD_GUILD_ID=your_discord_server_id
DISCORD_CHANNEL_ID=your_discord_channel_id
DISCORD_ALLOWED_ROLE_IDS=role_id_1,role_id_2
DISCORD_ALLOWED_USER_IDS=user_id_1,user_id_2

# Google AI Integration
GOOGLE_AI_API_KEY=your_google_gemini_api_key

# BetterStack Monitoring (Optional)
BETTERSTACK_API_KEY=your_betterstack_api_key

# PayPal Configuration
VITE_PAYPAL_SANDBOX=true_or_false
VITE_PAYPAL_SANDBOX_CLIENT_ID=your_paypal_sandbox_client_id
VITE_PAYPAL_SANDBOX_SECRET=your_paypal_sandbox_secret
VITE_PAYPAL_CLIENT_ID=your_paypal_live_client_id
VITE_PAYPAL_SECRET=your_paypal_live_secret
VITE_PAYPAL_CURRENCY=USD

# Application Settings
PORT=3000
NODE_ENV=development
```

---

## 💻 Installation & Development

```bash
# Clone the repository
git clone https://github.com/skyvps360/SkyPANEL.git
cd SkyPANEL

# Install dependencies
npm install

# Initialize the database
npm run db:push

# Create an admin user (interactive prompt)
npx tsx scripts/create-admin-user.ts

# Start development server
npm run dev

# Build for production
npm run build

# Start production server
npm start
```

### Package Management

To keep your packages up to date:

```bash
# Check for outdated packages and update them
npm run update-packages

# Install updated packages
npm install
```

---

## 🔄 VirtFusion Integration Guide

### Setting Up VirtFusion API Access

1. **Obtain API Credentials**: Get your API URL and key from VirtFusion admin panel
2. **Configure Environment**: Set `VIRTFUSION_API_URL` and `VIRTFUSION_API_KEY`
3. **Test Connectivity**: Use admin test endpoints to verify connection

### User Integration

SkyPANEL handles VirtFusion user mapping through:

#### External Relation ID Mapping
- **IMPORTANT**: All VirtFusion API calls use our local `user.id` as the `extRelationId` parameter
- Example: `/users/{extRelationId}/byExtRelation/resetPassword` uses local user.id value
- This ensures proper mapping between SkyPANEL users and VirtFusion accounts

#### User Profile Synchronization
When users update their profiles, we sync:
- **Username/Name Changes**: Automatically synced to VirtFusion
- **Email Changes**: Updated in both systems simultaneously
- **Password Resets**: Uses VirtFusion API-generated secure passwords

### Password Integration

The password reset process fully integrates with VirtFusion:

1. **Admin/User Initiates Reset**: Password reset triggered from either interface
2. **VirtFusion API Call**: POST to `/users/{extRelationId}/byExtRelation/resetPassword`
3. **Secure Password Generation**: VirtFusion returns cryptographically secure password
4. **Database Update**: Local database updated with hashed VirtFusion password
5. **Synchronization**: Both systems maintain password consistency

```javascript
// Example VirtFusion password reset
const response = await virtFusionApi.resetUserPassword(user.id);
const temporaryPassword = response.data.password;
```

### Resource Usage Tracking

For billing and monitoring:

```javascript
// Fetch hourly statistics from VirtFusion
const response = await axios.get(
  `${apiBaseUrl}/selfService/hourlyStats/byUserExtRelationId/${userId}`,
  { headers: { Authorization: `Bearer ${apiToken}` } }
);

// Parse monthly usage data
const monthlyTotal = parseFloat(response.data.monthlyTotal.value);
```

### Server Management Integration

- **Real-Time Status**: Live server state monitoring
- **Power Control**: Direct VirtFusion power management
- **Resource Monitoring**: CPU, memory, disk, and network statistics
- **VNC Access**: Integrated VNC console management
- **Traffic Statistics**: Bandwidth usage tracking

---

## 🎨 Brand Theming System

SkyPANEL implements a comprehensive brand theming system for dynamic customization.

### Color System Overview

The theming system uses three main brand colors:

- **Primary Color**: Main buttons, navigation, and primary UI elements
- **Secondary Color**: Secondary actions, accents, and supporting elements
- **Accent Color**: Highlights, callouts, badges, and attention elements

### Features

- **Interactive Color Pickers**: Visual color selection in admin settings
- **Hex Code Input**: Direct hex code entry for precise control
- **Color Presets**: Quick-selection harmonious color combinations
- **Real-time Preview**: Live preview of color changes on UI components
- **Automatic Variations**: Generated light/dark/opacity variations
- **Shadcn UI Integration**: Seamless integration via CSS variables

### Implementation

Colors are stored in the database and applied dynamically:

- **Database Storage**: Hex values stored without # prefix
- **Runtime Generation**: CSS variables generated and applied to document root
- **Component Integration**: Shadcn UI components automatically use brand colors
- **Backward Compatibility**: Legacy `companyColor` setting maintained

### Admin Configuration

To customize brand colors:

1. Navigate to **Admin → Settings → General**
2. Use the **Brand Colors** section:
   - Click color swatches to open color pickers
   - Enter hex codes directly in input fields
   - Select from preset color combinations
3. Preview changes in real-time
4. Click **Save Changes** to apply globally

### Technical Implementation

Key functions in `client/src/lib/brand-theme.ts`:

- `getBrandColors()`: Generates color variations for each brand color
- `hexToHSL()`: Converts hex colors to HSL for Shadcn UI
- `applyBrandColorVars()`: Applies variables to document root
- `applyToShadcnTheme()`: Updates Shadcn UI theme variables

---

## 💬 Discord Integration

SkyPANEL offers comprehensive Discord integration with webhook notifications and a powerful two-way communication bot.

### Webhook Notifications

#### Setup Process
1. **Create Discord Webhook**: Set up webhook in your Discord server settings
2. **Configure URL**: Add webhook URL to admin panel or environment variables
3. **Role Mentions**: Optionally configure role ID for notifications

#### Notification Types
- **Ticket Events**: Creation, replies, status changes, and deletions
- **System Alerts**: Maintenance mode and service status updates
- **Billing Events**: Payment confirmations and transaction updates
- **User Events**: Registration and account changes

#### Example Webhook Payload
```javascript
const payload = {
  content: roleId ? `<@&${roleId}>` : '',
  embeds: [{
    title: `New Support Ticket: ${ticket.subject}`,
    description: `A new support ticket has been created.`,
    color: 3447003, // Blue color
    fields: [
      { name: 'Ticket ID', value: `#${ticket.id}`, inline: true },
      { name: 'User', value: userName, inline: true },
      { name: 'Priority', value: ticket.priority || 'Medium', inline: true },
    ],
    footer: { text: `SkyPANEL - ${companyName}` },
    timestamp: new Date().toISOString()
  }]
};
```

### Discord Bot Integration

The system includes a powerful Discord bot for two-way ticket management.

#### Bot Commands

##### `/ask` - AI-Powered Support
- **Purpose**: Get intelligent responses using Google Gemini AI
- **Usage**: `/ask question: How do I reset my VPS password?`
- **Features**:
  - Context-aware responses based on hosting environment
  - Company branding and policy integration
  - Rate limiting and content moderation
  - Fallback to human support when needed

##### `/status` - Platform Monitoring
- **Purpose**: Display real-time platform status and health
- **Usage**: `/status`
- **Features**:
  - Live service availability indicators
  - BetterStack integration for monitoring data
  - Incident reporting and history
  - SLA monitoring and uptime statistics
  - Available anywhere in Discord (not restricted to threads)

##### `/ticket` - Support Management
- **Purpose**: Manage support tickets directly from Discord
- **Available Commands**:
  - `/ticket close` - Close the current ticket thread
  - `/ticket reopen` - Reopen a closed ticket thread
- **Features**:
  - Two-way synchronization with ticket system
  - Automatic thread creation for new tickets
  - Status updates in both platforms
  - Interactive buttons for quick actions

#### Permission System

The Discord bot includes sophisticated access control:

- **Role-Based Access**: Configure which Discord roles can use commands
- **User-Specific Permissions**: Grant access to individual users
- **Admin Interface**: Manage permissions through SkyPANEL admin panel
- **Granular Control**: Different permission levels for different commands
- **Security**: Prevents unauthorized access to sensitive operations

#### Two-Way Integration Features

- **Automatic Thread Creation**: New tickets create Discord threads
- **Message Synchronization**: Discord messages sync to ticket system
- **Status Reflection**: Ticket status changes update Discord thread names
- **Real-Time Notifications**: Instant notifications for ticket updates
- **Thread Management**: Automatic archiving based on ticket status

---

## 🤖 AI-Powered Support

SkyPANEL integrates Google Gemini 2.5 Flash for intelligent customer support automation.

### AI Features

#### Intelligent Response Generation
- **Context-Aware**: AI understands ticket context and history
- **Company Branding**: Responses include company-specific information
- **Technical Accuracy**: Trained on hosting and VPS management topics
- **Fallback Handling**: Graceful degradation when AI cannot help

#### Content Safety & Moderation
- **Built-in Filters**: Automatic content moderation for safety
- **Rate Limiting**: Prevents API abuse and manages costs
- **Error Handling**: Robust fallback mechanisms for API failures
- **Audit Logging**: Complete logging of AI interactions

### Implementation

#### Discord Bot Integration
```javascript
// AI-powered /ask command
const aiResponse = await geminiService.generateResponse(question, {
  context: 'hosting_support',
  company: companyName,
  user: interaction.user.id
});
```

#### Support Ticket Assistance
- **Response Suggestions**: AI suggests responses for support agents
- **Ticket Classification**: Automatic categorization and priority assignment
- **Knowledge Base**: AI draws from documentation and FAQs
- **Escalation Detection**: Identifies when human intervention is needed

#### Configuration

AI features can be configured in the admin panel:

- **API Key Management**: Secure Google AI API key storage
- **Response Templates**: Customizable AI response templates
- **Rate Limiting**: Configurable usage limits and quotas
- **Content Filters**: Adjustable safety and moderation settings

---

## 🌐 VNC Console

SkyPANEL includes a built-in web-based VNC client for direct server access.

### Key Features

#### Browser-Based Access
- **No Software Required**: Works in any modern web browser
- **Cross-Platform**: Compatible with Windows, macOS, Linux, and mobile
- **Real-Time Control**: Full mouse and keyboard control with live streaming
- **Responsive Design**: Adapts to different screen sizes and devices

#### VirtFusion Integration
- **Seamless Management**: Enable/disable VNC through VirtFusion API
- **Automatic Configuration**: Retrieves VNC credentials automatically
- **Security**: Uses VirtFusion server authentication
- **Status Monitoring**: Real-time VNC connection status

### Technical Implementation

#### Custom VNC Client
- **Modern Web Technologies**: Built specifically for SkyPANEL
- **WebSocket Communication**: Real-time data transmission
- **Framebuffer Updates**: Efficient screen rendering for smooth performance
- **Protocol Support**: Full VNC RFB protocol implementation

#### Connection Management
- **Automatic Retry**: Exponential backoff for connection failures
- **Timeout Handling**: 30+ second timeouts for reliable connectivity
- **Port Testing**: Automatic testing of multiple VNC ports (5900-5910)
- **Error Recovery**: Intelligent error handling and recovery

### Usage Instructions

#### For Users
1. **Navigate to Server**: Go to server details in user dashboard
2. **Check VNC Status**: View current VNC connection status
3. **Request Access**: Contact admin if VNC is not enabled
4. **Connect**: Click "Open VNC Console" when available

#### For Administrators
1. **Access Admin Panel**: Navigate to server management
2. **Enable VNC**: Click "Enable VNC" for the target server
3. **View Credentials**: See IP, port, and password information
4. **Launch Console**: Click "Open VNC Console" to access server
5. **Manage Access**: Enable/disable VNC as needed

### Security Features

#### Authentication & Access Control
- **VirtFusion Credentials**: Uses existing server authentication
- **Admin Restrictions**: VNC console access limited to administrators
- **Secure Connections**: All VNC traffic properly authenticated
- **Session Management**: Automatic cleanup of inactive sessions

#### Audit & Logging
- **Access Logging**: Complete logs of VNC access attempts
- **Action Tracking**: Records of VNC enable/disable operations
- **Security Monitoring**: Detection of unauthorized access attempts
- **Compliance**: Audit trails for security compliance

---

## 💵 Billing & Transaction System

SkyPANEL provides a comprehensive billing system with credit-based payments and professional invoicing.

### Transaction Types

#### Credit Operations
- **Credit Purchase**: Adding credits via PayPal payment processing
- **Credit Usage**: Automatic deductions for VirtFusion resource usage
- **Credit Refund**: Refunds processed to user accounts
- **Credit Addition**: Manual credit additions by administrators

#### Transaction Management
- **Real-Time Processing**: Instant transaction processing and confirmation
- **Audit Trail**: Complete transaction history with detailed logging
- **Status Tracking**: Pending, completed, failed, and canceled statuses
- **Reference Tracking**: PayPal transaction IDs and internal references

### Transaction Export System

The system provides professional PDF export functionality for transaction records:

#### Features
- **Professional Formatting**: Clean, branded transaction records
- **Complete Details**: Transaction IDs, dates, amounts, and descriptions
- **Export Options**: PDF export for users and administrators
- **Company Branding**: Dynamic company name and logo integration

#### Customization
```javascript
// Dynamic company branding in transaction exports
const companyName = await db.query.settings.findFirst({
  where: eq(schema.settings.key, 'company_name')
});
const displayName = companyName?.value || 'SkyPANEL';

// Professional transaction export formatting
doc.fontSize(20).font('Helvetica-Bold').text(displayName, { align: 'center' });
doc.text('<EMAIL>', { align: 'center' });
```

### PayPal Integration

#### Payment Processing
- **Secure Payments**: PayPal JavaScript SDK integration
- **Webhook Validation**: Automatic payment verification
- **Sandbox Support**: Development and testing environment
- **Multi-Currency**: Configurable currency support (USD default)

#### Configuration
```bash
# PayPal Environment Variables
VITE_PAYPAL_SANDBOX=true_or_false
VITE_PAYPAL_CLIENT_ID=your_paypal_client_id
VITE_PAYPAL_SECRET=your_paypal_secret
VITE_PAYPAL_CURRENCY=USD
```

### VirtFusion Billing Integration

#### Automatic Billing
- **Usage Tracking**: Real-time resource usage monitoring
- **Credit Deduction**: Automatic billing based on VirtFusion usage
- **Synchronization**: Two-way credit sync between systems
- **Reporting**: Detailed usage reports and analytics

#### Credit Management
```javascript
// Add credits to VirtFusion account
await virtFusionApi.addCreditToUser(user.id, {
  tokens: amount,
  reference_1: Date.now(),
  reference_2: `Added via SkyPANEL by ${admin.username}`
});
```

---

## 👥 User Management

SkyPANEL provides comprehensive user management with VirtFusion integration.

### User Registration Flow

#### Account Creation Process
1. **Registration Form**: User submits registration with email verification
2. **Account Creation**: Account created with pending verification status
3. **Email Verification**: Unique verification code sent via email
4. **Code Confirmation**: User enters verification code to activate account
5. **Account Activation**: Account marked as verified with full access

#### VirtFusion Integration
- **Automatic Sync**: New users automatically created in VirtFusion
- **External Relation ID**: Local user ID used as VirtFusion extRelationId
- **Profile Sync**: Real-time synchronization of profile changes
- **Credit Sync**: Automatic credit balance synchronization

### Password Management

#### User-Initiated Password Reset
1. **Forgot Password**: User requests reset via email
2. **Secure Token**: Cryptographically secure reset token generated
3. **Email Delivery**: Reset link sent to user's email address
4. **Token Verification**: User verifies identity with token
5. **Password Update**: New password set and synced with VirtFusion

#### Admin-Triggered Password Reset
1. **Admin Initiation**: Admin triggers reset from user management
2. **VirtFusion API**: System calls VirtFusion password reset API
3. **Secure Generation**: VirtFusion generates cryptographically secure password
4. **System Sync**: Password synchronized between both systems
5. **Notification**: User receives email with temporary password

#### Profile Password Reset
1. **Current Password**: User enters current password for verification
2. **VirtFusion API**: System generates new secure password via API
3. **Modal Display**: Temporary password shown in modal with copy function
4. **Email Confirmation**: Confirmation email sent with new password
5. **System Sync**: Password updated in both systems

### Profile Management

#### Synchronized Fields
- **Full Name/Username**: Real-time sync with VirtFusion user accounts
- **Email Address**: Updated simultaneously in both systems
- **Password**: VirtFusion API-generated secure passwords
- **Account Status**: Active/inactive status synchronized
- **Credit Balance**: Real-time credit balance synchronization

#### Admin User Management
- **User Search**: Advanced search and filtering capabilities
- **Bulk Operations**: Mass user operations and updates
- **Role Management**: Admin and user role assignments
- **Account Control**: Enable/disable user accounts
- **Credit Management**: Manual credit adjustments and tracking

### User Deletion with Server Checking

SkyPANEL implements comprehensive server checking before allowing user deletion to prevent data loss:

#### Server Verification Process
1. **Admin Initiates Deletion**: Admin clicks delete user from `/admin/users` page
2. **VirtFusion Server Check**: System checks if user has active servers using VirtFusion API
3. **Deletion Prevention**: If servers exist, deletion is completely blocked with detailed error message
4. **Safe Deletion**: Only users with no active servers can be deleted

#### Technical Implementation
- **API Endpoint**: Uses `/selfService/servers/byUserExtRelationId/{virtFusionUserId}` to check for servers
- **Error Handling**: Returns HTTP 409 status with server count and user-friendly error message
- **Data Integrity**: Prevents orphaned servers and maintains VirtFusion/SkyPANEL synchronization

```javascript
// Example server check before user deletion
const serversResponse = await virtFusionApi.getUserServers(user.virtFusionId);
if (serversResponse?.data?.length > 0) {
  // Block deletion - user has active servers
  return res.status(409).json({
    error: "Cannot delete user with active servers",
    serverCount: serversResponse.data.length
  });
}
```

#### Admin Workflow
1. **Manage Servers First**: All user servers must be deleted or transferred
2. **Verify No Servers**: System automatically verifies user has no active servers
3. **Safe Deletion**: Only then can the user account be safely removed

---

## 📝 Content Management

SkyPANEL includes a comprehensive content management system for blogs, documentation, and FAQs.

### Blog System

#### Features
- **Rich Text Editor**: Full-featured content editor with formatting
- **SEO Optimization**: Automatic slug generation and meta descriptions
- **Featured Images**: Image upload and management for blog posts
- **Publishing Control**: Draft, published, and scheduled post states
- **Author Attribution**: Posts associated with admin authors
- **Categories**: Organize posts with category system

#### Blog Data Model
```typescript
interface BlogPost {
  id: number;
  title: string;
  slug: string;           // URL-friendly identifier
  content: string;        // Rich text content
  author: string;         // Admin author
  publishedAt: Date;      // Publication date
  status: 'draft' | 'published' | 'scheduled';
  featuredImage?: string; // Optional header image
  metaDescription?: string; // SEO description
}
```

#### Public API
- **Public Blog Feed**: `/api/public/blog` - Get published posts
- **Post by Slug**: `/api/public/blog/:slug` - Get specific post
- **Admin Management**: Full CRUD operations in admin panel

### FAQ System

#### Features
- **Categorized Questions**: Group related questions for organization
- **Accordion Interface**: Expandable/collapsible answers
- **Search Functionality**: Find answers quickly with integrated search
- **Priority Ordering**: Arrange FAQs by importance or frequency
- **Rich Text Answers**: Format answers with lists, links, and styling

#### FAQ Data Model
```typescript
interface FAQ {
  id: number;
  question: string;       // The question text
  answer: string;         // Detailed response
  category: string;       // Grouping classification
  priority: number;       // Display order
  status: 'active' | 'inactive';
}
```

### Documentation System

#### Features
- **Hierarchical Structure**: Organized documentation with categories
- **Search Integration**: Full-text search across all documentation
- **Version Control**: Track changes and updates to documentation
- **Public Access**: Documentation available without authentication
- **Admin Management**: Full content management in admin panel

#### Implementation
- **Markdown Support**: Rich markdown rendering for documentation
- **Category Organization**: Logical grouping of documentation topics
- **Cross-References**: Internal linking between documentation pages
- **SEO Friendly**: Optimized URLs and meta information

### Datacenter Location Management

#### Interactive Map Features
- **World Map Visualization**: SVG-based world map with location markers
- **Location Details**: Detailed information for each datacenter
- **Status Indicators**: Active, coming soon, and inactive status
- **Regional Grouping**: Color-coded markers by geographic region

#### Admin Management
- **Location CRUD**: Create, read, update, delete datacenter locations
- **Coordinate Management**: Latitude/longitude positioning for map markers
- **Status Control**: Active/inactive status management
- **Display Order**: Custom ordering for location lists

#### Data Structure
```typescript
interface DatacenterLocation {
  id: number;
  code: string;           // Short identifier (e.g., "NYC", "LON")
  name: string;           // Full name (e.g., "New York", "London")
  country: string;        // Country name
  continent: string;      // Continental region
  latitude: number;       // Map positioning
  longitude: number;      // Map positioning
  status: 'active' | 'coming_soon' | 'inactive';
  features: string[];     // Available features
}
```

---

## 📊 Monitoring & Analytics

SkyPANEL integrates with BetterStack for comprehensive monitoring and provides detailed analytics.

### BetterStack Integration

#### Service Monitoring
- **Uptime Monitoring**: Real-time service availability tracking
- **Incident Management**: Automatic incident detection and reporting
- **SLA Monitoring**: Service level agreement tracking and reporting
- **Performance Metrics**: Response time and performance analytics

#### Status Page Integration
- **Public Status Page**: Real-time service status for customers
- **Incident History**: Historical incident tracking and resolution times
- **Maintenance Windows**: Scheduled maintenance notifications
- **Service Dependencies**: Monitor interconnected service health

#### Configuration
```bash
# BetterStack Environment Variables
BETTERSTACK_API_KEY=your_betterstack_api_key
```

### Platform Analytics

#### User Analytics
- **Registration Trends**: Track user growth and registration patterns
- **Activity Monitoring**: User engagement and platform usage statistics
- **Geographic Distribution**: User location and regional analytics
- **Retention Metrics**: User retention and churn analysis

#### Financial Analytics
- **Revenue Tracking**: Real-time revenue and transaction analytics
- **Payment Methods**: Payment method preferences and success rates
- **Credit Usage**: Credit consumption patterns and trends
- **Billing Efficiency**: Payment processing and collection metrics

#### System Performance
- **API Performance**: Endpoint response times and error rates
- **Database Performance**: Query performance and optimization metrics
- **Resource Usage**: Server resource consumption and scaling metrics
- **Error Tracking**: Application error monitoring and resolution

### Admin Dashboard Analytics

#### Real-Time Metrics
- **Active Users**: Currently online users and session tracking
- **System Health**: Live system status and performance indicators
- **Transaction Volume**: Real-time payment and billing activity
- **Support Metrics**: Ticket volume and response time tracking

#### Historical Reports
- **Growth Reports**: User and revenue growth over time
- **Usage Reports**: Platform feature usage and adoption
- **Performance Reports**: System performance trends and optimization
- **Financial Reports**: Detailed financial analytics and forecasting

---

## 🔑 API Key Management

SkyPANEL provides comprehensive API key management for secure third-party integrations.

### API Key Features

#### Security & Authentication
- **Secure Generation**: Cryptographically secure API key generation
- **Scoped Permissions**: Granular permission control for each API key
- **Rate Limiting**: Built-in rate limiting (100 requests per minute)
- **Expiration Management**: Optional API key expiration dates
- **Audit Logging**: Complete usage tracking and access logs

#### Available Scopes
- `read:user` - Read user profile information
- `read:servers` - Read server information and status
- `write:servers` - Create, update, or delete servers
- `read:billing` - View billing information and transactions
- `read:tickets` - View support tickets
- `write:tickets` - Create and update support tickets
- `admin:users` - Administrative access to user accounts (admin only)
- `admin:billing` - Administrative access to billing functions (admin only)
- `admin:system` - Administrative access to system settings (admin only)

### User API Key Management

#### Creating API Keys
1. **Navigate to Profile**: Go to user profile settings
2. **API Keys Section**: Access the API key management area
3. **Create New Key**: Click "Create New API Key"
4. **Select Scopes**: Choose required permissions for the key
5. **Generate Key**: System generates secure API key
6. **Copy Key**: Copy the key immediately (shown only once)

#### Managing API Keys
- **View Keys**: List all created API keys with creation dates
- **Scope Display**: View assigned scopes for each key
- **Usage Statistics**: Track API key usage and request counts
- **Delete Keys**: Remove API keys when no longer needed

### Admin API Key Management

#### System-Wide Monitoring
- **All API Keys**: View all user API keys across the platform
- **Usage Analytics**: Monitor API usage patterns and trends
- **Security Monitoring**: Detect unusual API usage patterns
- **Rate Limit Management**: Configure and monitor rate limiting

#### Security Features
- **Automatic Revocation**: Revoke compromised or suspicious keys
- **Usage Alerts**: Notifications for unusual API activity
- **Scope Auditing**: Track permission usage and access patterns
- **Compliance Reporting**: Generate API usage reports for compliance

---

## 🚧 Maintenance Mode

SkyPANEL includes a comprehensive maintenance mode system for planned downtime.

### Maintenance Mode Features

#### Activation & Control
- **Admin Control**: Enable/disable maintenance mode from admin panel
- **Bypass Tokens**: Generate secure tokens for admin access during maintenance
- **Custom Messages**: Configure custom maintenance messages for users
- **Scheduled Maintenance**: Plan and schedule maintenance windows

#### User Experience
- **Maintenance Page**: Professional maintenance page with company branding
- **Estimated Duration**: Display expected maintenance completion time
- **Status Updates**: Real-time updates on maintenance progress
- **Contact Information**: Support contact details during maintenance

### Implementation

#### Admin Controls
```bash
# Enable maintenance mode
POST /api/admin/maintenance/enable

# Disable maintenance mode
POST /api/admin/maintenance/disable

# Generate bypass token
GET /api/admin/maintenance/generate-token
```

#### Bypass System
- **Secure Tokens**: Cryptographically secure bypass tokens
- **Time-Limited**: Tokens expire after maintenance completion
- **Admin Access**: Full admin functionality during maintenance
- **Token Validation**: Secure token verification system

#### Notification Integration
- **Discord Notifications**: Automatic Discord notifications for maintenance events
- **Email Alerts**: Email notifications to administrators and users
- **Status Page Updates**: Automatic status page updates via BetterStack
- **API Responses**: Maintenance status included in API responses

---

## 🔒 Security Features

SkyPANEL implements enterprise-grade security measures throughout the platform.

### Authentication & Authorization

#### Password Security
- **Bcrypt Hashing**: Industry-standard password hashing with salt
- **VirtFusion Integration**: Secure password synchronization
- **Password Policies**: Configurable password strength requirements
- **Account Lockout**: Protection against brute force attacks

#### Session Management
- **Secure Sessions**: PostgreSQL-backed session storage
- **Session Expiration**: Configurable session timeout periods
- **CSRF Protection**: Cross-site request forgery protection
- **Secure Cookies**: HTTP-only and secure cookie flags

### Data Protection

#### Database Security
- **Encrypted Connections**: SSL/TLS encrypted database connections
- **Input Validation**: Comprehensive input sanitization and validation
- **SQL Injection Protection**: Parameterized queries and ORM protection
- **Data Encryption**: Sensitive data encryption at rest

#### API Security
- **Rate Limiting**: Comprehensive rate limiting across all endpoints
- **Input Validation**: Zod schema validation for all API inputs
- **Error Handling**: Secure error responses without information leakage
- **CORS Configuration**: Proper cross-origin resource sharing setup

### Monitoring & Auditing

#### Security Logging
- **Access Logs**: Comprehensive logging of all user actions
- **Failed Attempts**: Tracking of failed login and access attempts
- **API Usage**: Complete API usage logging and monitoring
- **Admin Actions**: Detailed logging of administrative actions

#### Threat Detection
- **Anomaly Detection**: Unusual activity pattern detection
- **IP Monitoring**: Suspicious IP address tracking
- **Rate Limit Violations**: Detection and blocking of abuse attempts
- **Security Alerts**: Real-time security event notifications

---

## 🔧 Troubleshooting

Common issues and their solutions for SkyPANEL deployment and operation.

### Installation Issues

#### Database Connection Problems
```bash
# Check database connection
npm run db:check

# Reset database schema
npm run db:reset

# Verify environment variables
echo $DATABASE_URL
```

#### VirtFusion API Issues
```bash
# Test VirtFusion connectivity
curl -H "Authorization: Bearer $VIRTFUSION_API_KEY" \
     "$VIRTFUSION_API_URL/test"

# Verify API credentials in admin panel
# Navigate to Admin → Settings → VirtFusion
```

### Common Runtime Issues

#### Email Delivery Problems
- **SMTP Configuration**: Verify SMTP2GO API key and settings
- **Email Templates**: Check email template configuration
- **Rate Limiting**: Ensure email rate limits are not exceeded
- **DNS Configuration**: Verify SPF and DKIM records

#### Payment Processing Issues
- **PayPal Configuration**: Verify PayPal client ID and secret
- **Webhook Validation**: Check PayPal webhook endpoint configuration
- **Sandbox Mode**: Ensure correct sandbox/production settings
- **Currency Settings**: Verify currency configuration matches PayPal

### Performance Optimization

#### Database Performance
```sql
-- Check slow queries
SELECT query, mean_time, calls
FROM pg_stat_statements
ORDER BY mean_time DESC
LIMIT 10;

-- Optimize frequently used indexes
CREATE INDEX CONCURRENTLY idx_users_email ON users(email);
CREATE INDEX CONCURRENTLY idx_tickets_status ON tickets(status);
```

#### Application Performance
- **Connection Pooling**: Configure PostgreSQL connection pooling
- **Caching**: Implement Redis caching for frequently accessed data
- **CDN Integration**: Use CDN for static asset delivery
- **Load Balancing**: Configure load balancing for high availability

### Monitoring & Debugging

#### Log Analysis
```bash
# View application logs
npm run logs

# Check error logs
tail -f logs/error.log

# Monitor API performance
grep "slow query" logs/app.log
```

#### Health Checks
```bash
# Check system health
curl http://localhost:3000/api/health

# Verify database connectivity
curl http://localhost:3000/api/db-health

# Test VirtFusion integration
curl http://localhost:3000/api/virtfusion-health
```

---

## 🆕 Recent Updates

### Version 2.0.0 - Major Platform Overhaul

#### New Features
- **🤖 AI-Powered Support**: Google Gemini 2.5 Flash integration for intelligent customer support
- **💬 Discord Bot**: Complete Discord integration with two-way ticket management
- **🌐 VNC Console**: Built-in web-based VNC client for direct server access
- **🎨 Brand Theming**: Advanced multi-color theming system with real-time preview
- **📊 Enhanced Analytics**: Comprehensive monitoring with BetterStack integration
- **🔑 API Key Management**: Granular API access control with scoped permissions

#### Technical Improvements
- **⚡ Performance**: Optimized database queries and improved caching
- **🔒 Security**: Enhanced authentication and authorization systems
- **📱 Mobile**: Improved responsive design and mobile experience
- **🔄 Real-Time**: WebSocket integration for live updates
- **🛠️ DevOps**: Improved deployment and monitoring capabilities

#### VirtFusion Integration Enhancements
- **🔄 Real-Time Sync**: Live synchronization of user data and server status
- **💰 Billing Integration**: Automatic credit deduction based on usage
- **🔐 Password Management**: Secure password reset via VirtFusion API
- **📊 Resource Monitoring**: Real-time server resource tracking

### Migration Guide

#### From Version 1.x
1. **Backup Database**: Create complete database backup before migration
2. **Update Dependencies**: Run `npm install` to update all packages
3. **Database Migration**: Execute `npm run db:push` to apply schema changes
4. **Environment Variables**: Add new environment variables for AI and Discord
5. **Test Integration**: Verify VirtFusion and payment integrations

#### Breaking Changes
- **API Endpoints**: Some API endpoints have been restructured for consistency
- **Database Schema**: New tables added for Discord integration and API keys
- **Authentication**: Enhanced session management may require re-login
- **Configuration**: New settings added for AI and monitoring features

---

## 📞 Support & Community

### Getting Help

#### Documentation
- **API Documentation**: Complete API reference with examples
- **Integration Guides**: Step-by-step integration tutorials
- **Troubleshooting**: Common issues and solutions
- **Best Practices**: Recommended deployment and configuration practices

#### Community Support
- **GitHub Issues**: Report bugs and request features
- **Discord Community**: Join our Discord server for real-time support
- **Email Support**: Contact <EMAIL> for assistance
- **Professional Support**: Enterprise support packages available

### Contributing

#### Development
- **Fork Repository**: Create your own fork for development
- **Feature Branches**: Use feature branches for new development
- **Pull Requests**: Submit pull requests with detailed descriptions
- **Code Review**: All changes undergo thorough code review

#### Bug Reports
- **Issue Templates**: Use provided templates for bug reports
- **Reproduction Steps**: Include detailed steps to reproduce issues
- **Environment Details**: Provide system and configuration information
- **Logs**: Include relevant log files and error messages

---

## 📄 License

SkyPANEL is released under the MIT License. See the [LICENSE](LICENSE) file for details.

### Commercial Use
- **Free for Commercial Use**: Use SkyPANEL for commercial hosting businesses
- **White-Label Ready**: Remove branding and customize for your business
- **No Licensing Fees**: No ongoing licensing or usage fees
- **Support Available**: Professional support and customization services available

### Attribution
While not required, attribution is appreciated:
- **GitHub Star**: Star the repository if you find it useful
- **Social Media**: Share your SkyPANEL deployment on social media
- **Community**: Contribute back to the community with improvements

---

<div align="center">
  <h3>🌟 Thank you for choosing SkyPANEL! 🌟</h3>
  <p>Built with ❤️ by the SkyPANEL team</p>
</div>
